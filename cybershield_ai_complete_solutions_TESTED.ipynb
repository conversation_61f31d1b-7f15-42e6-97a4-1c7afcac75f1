# Week 1 Solution: Environment Setup and Basic AI Integration (TESTED)

import os
from datetime import datetime
import json

# Mock OpenAI for testing without API key
class MockOpenAI:
    class ChatCompletions:
        class Create:
            def create(self, **kwargs):
                class MockResponse:
                    def __init__(self):
                        self.choices = [MockChoice()]
                        self.usage = MockUsage()
                
                class MockChoice:
                    def __init__(self):
                        self.message = MockMessage()
                
                class MockMessage:
                    def __init__(self):
                        self.content = """
                        THREAT ASSESSMENT: HIGH
                        
                        ANALYSIS:
                        Multiple failed login attempts from IP ************ targeting admin accounts indicates a potential brute force attack.
                        
                        IMMEDIATE ACTIONS:
                        1. Block the source IP address immediately
                        2. Review admin account access logs
                        3. Implement account lockout policies
                        
                        LONG-TERM RECOMMENDATIONS:
                        1. Implement multi-factor authentication
                        2. Set up intrusion detection systems
                        3. Regular security awareness training
                        """
                
                class MockUsage:
                    def __init__(self):
                        self.total_tokens = 150
                
                return MockResponse()
        
        def __init__(self):
            self.completions = self.Create()
    
    def __init__(self, api_key=None):
        self.chat = self.ChatCompletions()

def analyze_security_incident(incident_description, use_mock=True):
    """
    Basic security incident analysis using OpenAI.
    
    Args:
        incident_description (str): Description of the security incident
        use_mock (bool): Whether to use mock client for testing
    
    Returns:
        dict: Analysis results with recommendations
    """
    try:
        if use_mock:
            client = MockOpenAI()
        else:
            try:
                import openai
                client = openai.OpenAI(
                    api_key=os.getenv('OPENAI_API_KEY', 'your-api-key-here')
                )
            except ImportError:
                print('OpenAI package not installed. Using mock client.')
                client = MockOpenAI()
        
        prompt = f"""
        You are a cybersecurity analyst. Analyze the following security incident and provide:
        1. Threat assessment (Low/Medium/High/Critical)
        2. Potential attack vectors
        3. Immediate actions to take
        4. Long-term recommendations
        
        Incident: {incident_description}
        
        Provide your analysis in a structured format.
        """
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert cybersecurity analyst with 10+ years of experience in incident response and threat analysis."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.3
        )
        
        return {
            'success': True,
            'analysis': response.choices[0].message.content,
            'timestamp': datetime.now().isoformat(),
            'model_used': 'gpt-4'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Test the function
print("Week 1: Basic Security Analysis")
print("=" * 40)

test_incident = "Multiple failed login attempts detected from IP ************ targeting admin accounts"
result = analyze_security_incident(test_incident, use_mock=True)

print(f"Success: {result['success']}")
if result['success']:
    print(f"Analysis preview: {result['analysis'][:200]}...")
    print(f"Timestamp: {result['timestamp']}")
    print(f"Model used: {result['model_used']}")
else:
    print(f"Error: {result['error']}")

print("\n✅ Week 1 completed successfully!")

# Week 5 Solution: Enhanced PII Masker with SpaCy Integration (TESTED & FIXED)

import re
from typing import Dict, List, Any
from datetime import datetime

# Base PII Masker class
class PIIMasker:
    def __init__(self):
        self.pii_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'\b(?:\+?1[-.]?)?\(?([0-9]{3})\)?[-.]?([0-9]{3})[-.]?([0-9]{4})\b',
            'ssn': r'\b(?!000|666|9\d{2})\d{3}[-.]?(?!00)\d{2}[-.]?(?!0000)\d{4}\b',
            'person_name': r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'
        }
        
    def detect_pii(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        detections = {}
        for pii_type, pattern in self.pii_patterns.items():
            matches = []
            for match in re.finditer(pattern, text, re.IGNORECASE):
                matches.append({
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end(),
                    'type': pii_type
                })
            if matches:
                detections[pii_type] = matches
        return detections

class EnhancedPIIMasker(PIIMasker):
    """
    Enhanced PII masker that combines regex patterns with SpaCy's NLP
    capabilities for comprehensive PII detection and protection.
    """
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        # Initialize parent class
        super().__init__()
        
        # Try to load SpaCy model
        self.nlp = None
        self.spacy_available = False
        
        try:
            import spacy
            from spacy.matcher import Matcher
            
            self.nlp = spacy.load(model_name)
            self.spacy_available = True
            print(f"✅ Loaded SpaCy model: {model_name}")
            
            # SpaCy entity types that may contain PII
            self.spacy_pii_types = {
                'PERSON': 'person',
                'ORG': 'organization',
                'GPE': 'location',
                'MONEY': 'money',
                'DATE': 'date',
                'TIME': 'time'
            }
            
            # Initialize custom matcher
            self.matcher = Matcher(self.nlp.vocab)
            self._add_custom_patterns()
            
        except (ImportError, OSError) as e:
            print(f"⚠️ SpaCy not available: {e}")
            print("   Falling back to regex-only detection")
            print("   To enable SpaCy: pip install spacy && python -m spacy download en_core_web_sm")
        
        # Performance tracking
        self.performance_stats = {
            'regex_detections': 0,
            'spacy_detections': 0,
            'total_processing_time': 0.0,
            'documents_processed': 0
        }
    
    def _add_custom_patterns(self):
        """Add custom patterns to the SpaCy matcher."""
        if not self.spacy_available:
            return
            
        # Email patterns
        email_patterns = [[{"LIKE_EMAIL": True}]]
        self.matcher.add("EMAIL", email_patterns)
        
        # Phone patterns
        phone_patterns = [
            [{"TEXT": "(", "OP": "?"}, {"SHAPE": "ddd"}, {"TEXT": ")", "OP": "?"}, 
             {"TEXT": {"IN": ["-", ".", " "]}, "OP": "?"}, {"SHAPE": "ddd"}, 
             {"TEXT": {"IN": ["-", "."]}, "OP": "?"}, {"SHAPE": "dddd"}]
        ]
        self.matcher.add("PHONE", phone_patterns)
    
    def analyze_text_entities(self, text: str) -> Dict[str, Any]:
        """
        Analyze text using SpaCy NER to identify entities that may contain PII.
        """
        start_time = datetime.now()
        
        if not self.spacy_available:
            return {
                'spacy_entities': [],
                'custom_matches': [],
                'processing_time': 0.0,
                'total_entities': 0,
                'pii_entities': [],
                'error': 'SpaCy not available'
            }
        
        # Process text with SpaCy
        doc = self.nlp(text)
        
        # Extract named entities
        spacy_entities = []
        for ent in doc.ents:
            entity_info = {
                'text': ent.text,
                'label': ent.label_,
                'start': ent.start_char,
                'end': ent.end_char,
                'confidence': 0.9,
                'description': getattr(ent, 'label_', 'Unknown')
            }
            
            # Check if this entity type is considered PII
            if ent.label_ in self.spacy_pii_types:
                entity_info['is_pii'] = True
                entity_info['pii_type'] = self.spacy_pii_types[ent.label_]
            else:
                entity_info['is_pii'] = False
                entity_info['pii_type'] = None
            
            spacy_entities.append(entity_info)
        
        # Apply custom patterns
        custom_matches = []
        try:
            matches = self.matcher(doc)
            for match_id, start, end in matches:
                span = doc[start:end]
                label = self.nlp.vocab.strings[match_id]
                
                custom_matches.append({
                    'text': span.text,
                    'label': label,
                    'start': span.start_char,
                    'end': span.end_char,
                    'confidence': 0.95,
                    'is_pii': True,
                    'pii_type': label.lower(),
                    'method': 'custom_pattern'
                })
        except Exception as e:
            print(f"Warning: Custom pattern matching failed: {e}")
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Update performance stats
        self.performance_stats['spacy_detections'] += len(spacy_entities) + len(custom_matches)
        self.performance_stats['total_processing_time'] += processing_time
        self.performance_stats['documents_processed'] += 1
        
        return {
            'spacy_entities': spacy_entities,
            'custom_matches': custom_matches,
            'processing_time': processing_time,
            'total_entities': len(spacy_entities) + len(custom_matches),
            'pii_entities': [e for e in spacy_entities + custom_matches if e.get('is_pii', False)]
        }
    
    def enhanced_pii_detection(self, text: str) -> Dict[str, Any]:
        """
        Perform enhanced PII detection combining regex and SpaCy approaches.
        """
        start_time = datetime.now()
        
        # Get regex-based detections
        regex_detections = self.detect_pii(text)
        self.performance_stats['regex_detections'] += sum(len(matches) for matches in regex_detections.values())
        
        # Get SpaCy-based detections
        spacy_analysis = self.analyze_text_entities(text)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return {
            'regex_detections': regex_detections,
            'spacy_analysis': spacy_analysis,
            'processing_time': processing_time,
            'detection_summary': {
                'total_pii_items': sum(len(matches) for matches in regex_detections.values()) + len(spacy_analysis.get('pii_entities', [])),
                'detection_methods': ['regex'] + (['spacy_ner', 'custom_patterns'] if self.spacy_available else [])
            }
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = self.performance_stats.copy()
        
        if stats['documents_processed'] > 0:
            stats['avg_processing_time'] = stats['total_processing_time'] / stats['documents_processed']
            stats['avg_detections_per_doc'] = (stats['regex_detections'] + stats['spacy_detections']) / stats['documents_processed']
        else:
            stats['avg_processing_time'] = 0.0
            stats['avg_detections_per_doc'] = 0.0
        
        return stats

# Test the enhanced implementation
print("Week 5: Enhanced PII Masker with SpaCy Integration (FIXED)")
print("=" * 60)

try:
    # Initialize enhanced masker
    enhanced_masker = EnhancedPIIMasker()
    
    # Complex test text
    complex_text = """
    Dr. Sarah Johnson, a cardiologist at General Hospital, can be reached at 
    <EMAIL> or by phone at (*************. Her patient 
    John Doe (SSN: ***********) has a credit card ending in 9012.
    """
    
    print("Test Text:")
    print(complex_text.strip())
    
    # Test entity analysis
    print("\nEntity Analysis:")
    print("-" * 40)
    entity_analysis = enhanced_masker.analyze_text_entities(complex_text)
    
    if 'error' in entity_analysis:
        print(f"SpaCy Analysis: {entity_analysis['error']}")
    else:
        print(f"SpaCy Entities Found: {len(entity_analysis['spacy_entities'])}")
        for entity in entity_analysis['spacy_entities'][:5]:  # Show first 5
            print(f"  - {entity['text']} ({entity['label']}) - PII: {entity['is_pii']}")
        
        print(f"\nCustom Pattern Matches: {len(entity_analysis['custom_matches'])}")
        for match in entity_analysis['custom_matches'][:5]:  # Show first 5
            print(f"  - {match['text']} ({match['label']}) - Confidence: {match['confidence']}")
    
    # Test enhanced detection
    print("\nEnhanced PII Detection:")
    print("-" * 40)
    enhanced_results = enhanced_masker.enhanced_pii_detection(complex_text)
    
    print(f"Total PII Items: {enhanced_results['detection_summary']['total_pii_items']}")
    print(f"Processing Time: {enhanced_results['processing_time']:.3f} seconds")
    print(f"Detection Methods: {', '.join(enhanced_results['detection_summary']['detection_methods'])}")
    
    # Show regex detections
    print("\nRegex Detections:")
    for pii_type, matches in enhanced_results['regex_detections'].items():
        print(f"  {pii_type}: {len(matches)} items")
    
    # Test performance stats
    print("\nPerformance Statistics:")
    print("-" * 40)
    perf_stats = enhanced_masker.get_performance_stats()
    print(f"Documents Processed: {perf_stats['documents_processed']}")
    print(f"Average Processing Time: {perf_stats['avg_processing_time']:.3f} seconds")
    
    print("\n✅ Week 5 completed successfully!")
    
except Exception as e:
    print(f"❌ Error in Week 5: {str(e)}")
    import traceback
    traceback.print_exc()