{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "header",
   "metadata": {},
   "source": [
    "# CyberShield AI: Complete 8-Week Course Solutions\n",
    "## A Comprehensive Cybersecurity Conversational Agent Development Course\n",
    "\n",
    "**Instructor:** <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, Ph.D.  \n",
    "**Course Duration:** 8 Weeks  \n",
    "**Programming Language:** Python 3  \n",
    "**Platform:** Google Colab Compatible  \n",
    "\n",
    "---\n",
    "\n",
    "### Course Overview\n",
    "\n",
    "This notebook contains complete solutions and detailed explanations for the 8-week CyberShield AI course. Each week builds upon the previous one, culminating in a production-ready cybersecurity AI agent capable of:\n",
    "\n",
    "- **PII Detection & Protection**: Advanced masking using regex and NLP\n",
    "- **Security API Integration**: Shodan, VirusTotal, and AbuseIPDB\n",
    "- **Multimodal Capabilities**: Text and image analysis\n",
    "- **ReACT Framework**: Reasoning and Acting for intelligent responses\n",
    "- **Production Deployment**: Scalable, secure, and compliant\n",
    "\n",
    "### Learning Objectives\n",
    "\n",
    "By the end of this course, students will be able to:\n",
    "1. Implement robust PII detection and masking systems\n",
    "2. Integrate multiple cybersecurity APIs for threat intelligence\n",
    "3. Build AI agents using the ReACT framework\n",
    "4. Handle multimodal inputs (text and images)\n",
    "5. Deploy production-ready cybersecurity applications\n",
    "\n",
    "### Prerequisites\n",
    "\n",
    "- Basic Python programming knowledge\n",
    "- Understanding of HTTP requests and APIs\n",
    "- Familiarity with cybersecurity concepts\n",
    "- Google Colab account for running notebooks\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week1",
   "metadata": {},
   "source": [
    "# Week 1: Prerequisites and Fundamentals\n",
    "\n",
    "## Learning Objectives\n",
    "- Understand cybersecurity concepts and threat models\n",
    "- Learn about security APIs and their applications\n",
    "- Review Python fundamentals for cybersecurity\n",
    "- Introduction to Large Language Models and ReACT technique\n",
    "- Understand PII (Personally Identifiable Information) significance\n",
    "\n",
    "## Theoretical Foundation\n",
    "\n",
    "### Cybersecurity Fundamentals\n",
    "\n",
    "**CIA Triad**: The foundation of information security\n",
    "- **Confidentiality**: Ensuring information is accessible only to authorized individuals\n",
    "- **Integrity**: Maintaining accuracy and completeness of data\n",
    "- **Availability**: Guaranteeing reliable access to information when needed\n",
    "\n",
    "**Defense in Depth**: Multiple layers of security controls:\n",
    "- Physical security\n",
    "- Network security\n",
    "- Application security\n",
    "- Data security\n",
    "- Identity and access management\n",
    "\n",
    "### Security APIs Overview\n",
    "\n",
    "**Shodan**: Internet-connected device discovery\n",
    "- Discovers exposed devices and services\n",
    "- Identifies vulnerabilities\n",
    "- Provides historical data\n",
    "\n",
    "**VirusTotal**: Malware analysis platform\n",
    "- Multi-engine file scanning\n",
    "- URL reputation checking\n",
    "- Behavioral analysis\n",
    "\n",
    "**AbuseIPDB**: IP reputation database\n",
    "- Reports malicious IP addresses\n",
    "- Provides abuse confidence scores\n",
    "- Community-driven threat intelligence\n",
    "\n",
    "### PII (Personally Identifiable Information)\n",
    "\n",
    "**Definition**: Any information that can identify a specific individual\n",
    "\n",
    "**Common PII Types**:\n",
    "- Names and addresses\n",
    "- Social Security Numbers\n",
    "- Email addresses and phone numbers\n",
    "- Financial account numbers\n",
    "- Biometric data\n",
    "\n",
    "**Regulatory Compliance**:\n",
    "- GDPR (General Data Protection Regulation)\n",
    "- CCPA (California Consumer Privacy Act)\n",
    "- HIPAA (Health Insurance Portability and Accountability Act)\n",
    "\n",
    "### ReACT Framework\n",
    "\n",
    "**ReACT** (Reasoning and Acting) enables LLMs to:\n",
    "- **Think**: Reason about the current state\n",
    "- **Act**: Perform specific actions\n",
    "- **Observe**: Incorporate feedback\n",
    "- **Repeat**: Continue until task completion\n",
    "\n",
    "## Homework Assignment\n",
    "\n",
    "**Task**: Research and document the following:\n",
    "1. Create a threat model for a small business\n",
    "2. Identify 5 types of PII and their regulatory requirements\n",
    "3. Research one recent cybersecurity incident involving PII\n",
    "4. Explain how the ReACT framework could help in incident response\n",
    "\n",
    "**Deliverable**: A 2-page report covering all four topics with proper citations.\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week2",
   "metadata": {},
   "source": [
    "# Week 2: Basic Cybersecurity Tools Integration\n",
    "\n",
    "## Learning Objectives\n",
    "- Set up development environment and libraries\n",
    "- Implement API authentication for cybersecurity tools\n",
    "- Create basic functions for Shodan, VirusTotal, and AbuseIPDB\n",
    "- Understand error handling and rate limiting\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week2-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 2: Install necessary libraries\n",
    "!pip install requests python-dotenv shodan\n",
    "\n",
    "print(\"✅ Libraries installed successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week2-imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 2: Required imports\n",
    "import requests\n",
    "import json\n",
    "import re\n",
    "import time\n",
    "import os\n",
    "from typing import Dict, Any, Optional\n",
    "import shodan\n",
    "\n",
    "print(\"✅ All imports successful!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week2-config",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 2: API Configuration\n",
    "# IMPORTANT: Replace these with your actual API keys\n",
    "# For Google Colab, you can use the secrets feature or environment variables\n",
    "\n",
    "# API Keys - Replace with your actual keys\n",
    "OPENAI_API_KEY = \"********************************************************************************************************************************************************************\"\n",
    "VIRUSTOTAL_API_KEY = \"****************************************************************\"\n",
    "ABUSEIPDB_API_KEY = \"********************************************************************************\"\n",
    "SHODAN_API_KEY = \"********************************\"\n",
    "\n",
    "# Set environment variables\n",
    "os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY\n",
    "os.environ['VIRUSTOTAL_API_KEY'] = VIRUSTOTAL_API_KEY\n",
    "os.environ['ABUSEIPDB_API_KEY'] = ABUSEIPDB_API_KEY\n",
    "os.environ['SHODAN_API_KEY'] = SHODAN_API_KEY\n",
    "\n",
    "print(\"✅ API keys configured successfully!\")\n",
    "print(\"⚠️  Remember: Never commit API keys to version control!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week2-functions",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 2: Cybersecurity Tool Functions\n",
    "\n",
    "def regex_checker(pattern: str, text: str) -> Dict[str, Any]:\n",
    "    \"\"\"\n",
    "    Check if a regex pattern matches any part of the text.\n",
    "    \n",
    "    Args:\n",
    "        pattern (str): Regular expression pattern to search for\n",
    "        text (str): Text to search within\n",
    "    \n",
    "    Returns:\n",
    "        Dict[str, Any]: Dictionary containing matches and metadata\n",
    "    \"\"\"\n",
    "    try:\n",
    "        # Compile the regex pattern\n",
    "        compiled_pattern = re.compile(pattern, re.IGNORECASE)\n",
    "        \n",
    "        # Find all matches\n",
    "        matches = compiled_pattern.findall(text)\n",
    "        \n",
    "        # Find match positions\n",
    "        match_positions = []\n",
    "        for match in compiled_pattern.finditer(text):\n",
    "            match_positions.append({\n",
    "                'match': match.group(),\n",
    "                'start': match.start(),\n",
    "                'end': match.end()\n",
    "            })\n",
    "        \n",
    "        result = {\n",
    "            'pattern': pattern,\n",
    "            'matches_found': len(matches),\n",
    "            'matches': matches,\n",
    "            'positions': match_positions,\n",
    "            'success': True\n",
    "        }\n",
    "        \n",
    "        return result\n",
    "        \n",
    "    except re.error as e:\n",
    "        return {\n",
    "            'pattern': pattern,\n",
    "            'error': f\"Invalid regex pattern: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "    except Exception as e:\n",
    "        return {\n",
    "            'pattern': pattern,\n",
    "            'error': f\"Unexpected error: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "\n",
    "def shodan_lookup(ip: str) -> Dict[str, Any]:\n",
    "    \"\"\"\n",
    "    Perform a Shodan lookup for an IP address.\n",
    "    \n",
    "    Args:\n",
    "        ip (str): IP address to lookup\n",
    "    \n",
    "    Returns:\n",
    "        Dict[str, Any]: Shodan API response data\n",
    "    \"\"\"\n",
    "    try:\n",
    "        # Initialize Shodan API client\n",
    "        api = shodan.Shodan(SHODAN_API_KEY)\n",
    "        \n",
    "        # Perform the lookup\n",
    "        host_info = api.host(ip)\n",
    "        \n",
    "        # Extract relevant information\n",
    "        result = {\n",
    "            'ip': ip,\n",
    "            'organization': host_info.get('org', 'Unknown'),\n",
    "            'operating_system': host_info.get('os', 'Unknown'),\n",
    "            'ports': host_info.get('ports', []),\n",
    "            'hostnames': host_info.get('hostnames', []),\n",
    "            'country': host_info.get('country_name', 'Unknown'),\n",
    "            'city': host_info.get('city', 'Unknown'),\n",
    "            'isp': host_info.get('isp', 'Unknown'),\n",
    "            'vulnerabilities': host_info.get('vulns', []),\n",
    "            'last_update': host_info.get('last_update', 'Unknown'),\n",
    "            'success': True\n",
    "        }\n",
    "        \n",
    "        return result\n",
    "        \n",
    "    except shodan.APIError as e:\n",
    "        return {\n",
    "            'ip': ip,\n",
    "            'error': f\"Shodan API error: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "    except Exception as e:\n",
    "        return {\n",
    "            'ip': ip,\n",
    "            'error': f\"Unexpected error: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "\n",
    "def virustotal_lookup(resource: str, resource_type: str = \"ip\") -> Dict[str, Any]:\n",
    "    \"\"\"\n",
    "    Perform a VirusTotal lookup for a file hash, URL, or IP.\n",
    "    \n",
    "    Args:\n",
    "        resource (str): The resource to lookup (IP, URL, or file hash)\n",
    "        resource_type (str): Type of resource ('ip', 'url', 'file')\n",
    "    \n",
    "    Returns:\n",
    "        Dict[str, Any]: VirusTotal API response data\n",
    "    \"\"\"\n",
    "    try:\n",
    "        # API endpoint mapping\n",
    "        endpoints = {\n",
    "            'ip': f'https://www.virustotal.com/api/v3/ip_addresses/{resource}',\n",
    "            'url': 'https://www.virustotal.com/api/v3/urls',\n",
    "            'file': f'https://www.virustotal.com/api/v3/files/{resource}'\n",
    "        }\n",
    "        \n",
    "        headers = {\n",
    "            'x-apikey': VIRUSTOTAL_API_KEY,\n",
    "            'Content-Type': 'application/json'\n",
    "        }\n",
    "        \n",
    "        if resource_type == 'url':\n",
    "            # For URLs, we need to submit first, then get results\n",
    "            import base64\n",
    "            url_id = base64.urlsafe_b64encode(resource.encode()).decode().strip(\"=\")\n",
    "            url = f'https://www.virustotal.com/api/v3/urls/{url_id}'\n",
    "        else:\n",
    "            url = endpoints.get(resource_type)\n",
    "        \n",
    "        if not url:\n",
    "            return {\n",
    "                'resource': resource,\n",
    "                'error': f\"Invalid resource type: {resource_type}\",\n",
    "                'success': False\n",
    "            }\n",
    "        \n",
    "        # Make the API request\n",
    "        response = requests.get(url, headers=headers)\n",
    "        \n",
    "        if response.status_code == 200:\n",
    "            data = response.json()\n",
    "            attributes = data.get('data', {}).get('attributes', {})\n",
    "            \n",
    "            # Extract relevant information\n",
    "            result = {\n",
    "                'resource': resource,\n",
    "                'resource_type': resource_type,\n",
    "                'reputation': attributes.get('reputation', 0),\n",
    "                'harmless': attributes.get('last_analysis_stats', {}).get('harmless', 0),\n",
    "                'malicious': attributes.get('last_analysis_stats', {}).get('malicious', 0),\n",
    "                'suspicious': attributes.get('last_analysis_stats', {}).get('suspicious', 0),\n",
    "                'undetected': attributes.get('last_analysis_stats', {}).get('undetected', 0),\n",
    "                'country': attributes.get('country', 'Unknown'),\n",
    "                'as_owner': attributes.get('as_owner', 'Unknown'),\n",
    "                'last_analysis_date': attributes.get('last_analysis_date', 'Unknown'),\n",
    "                'success': True\n",
    "            }\n",
    "            \n",
    "            return result\n",
    "        else:\n",
    "            return {\n",
    "                'resource': resource,\n",
    "                'error': f\"API request failed with status {response.status_code}: {response.text}\",\n",
    "                'success': False\n",
    "            }\n",
    "            \n",
    "    except Exception as e:\n",
    "        return {\n",
    "            'resource': resource,\n",
    "            'error': f\"Unexpected error: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "\n",
    "def abuseipdb_lookup(ip: str, max_age_days: int = 90) -> Dict[str, Any]:\n",
    "    \"\"\"\n",
    "    Perform an AbuseIPDB lookup for an IP address.\n",
    "    \n",
    "    Args:\n",
    "        ip (str): IP address to lookup\n",
    "        max_age_days (int): Maximum age of reports to consider\n",
    "    \n",
    "    Returns:\n",
    "        Dict[str, Any]: AbuseIPDB API response data\n",
    "    \"\"\"\n",
    "    try:\n",
    "        url = 'https://api.abuseipdb.com/api/v2/check'\n",
    "        \n",
    "        headers = {\n",
    "            'Key': ABUSEIPDB_API_KEY,\n",
    "            'Accept': 'application/json'\n",
    "        }\n",
    "        \n",
    "        params = {\n",
    "            'ipAddress': ip,\n",
    "            'maxAgeInDays': max_age_days,\n",
    "            'verbose': ''\n",
    "        }\n",
    "        \n",
    "        # Make the API request\n",
    "        response = requests.get(url, headers=headers, params=params)\n",
    "        \n",
    "        if response.status_code == 200:\n",
    "            data = response.json().get('data', {})\n",
    "            \n",
    "            result = {\n",
    "                'ip': ip,\n",
    "                'abuse_confidence': data.get('abuseConfidencePercentage', 0),\n",
    "                'country_code': data.get('countryCode', 'Unknown'),\n",
    "                'usage_type': data.get('usageType', 'Unknown'),\n",
    "                'isp': data.get('isp', 'Unknown'),\n",
    "                'domain': data.get('domain', 'Unknown'),\n",
    "                'total_reports': data.get('totalReports', 0),\n",
    "                'num_distinct_users': data.get('numDistinctUsers', 0),\n",
    "                'last_reported_at': data.get('lastReportedAt', 'Never'),\n",
    "                'is_public': data.get('isPublic', False),\n",
    "                'is_whitelisted': data.get('isWhitelisted', False),\n",
    "                'success': True\n",
    "            }\n",
    "            \n",
    "            return result\n",
    "        else:\n",
    "            return {\n",
    "                'ip': ip,\n",
    "                'error': f\"API request failed with status {response.status_code}: {response.text}\",\n",
    "                'success': False\n",
    "            }\n",
    "            \n",
    "    except Exception as e:\n",
    "        return {\n",
    "            'ip': ip,\n",
    "            'error': f\"Unexpected error: {str(e)}\",\n",
    "            'success': False\n",
    "        }\n",
    "\n",
    "print(\"✅ All cybersecurity tool functions implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week2-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 2: Test the functions with sample data\n",
    "\n",
    "print(\"🔍 Testing Cybersecurity Tool Functions\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Test 1: Regex Checker\n",
    "print(\"\\n1. Testing Regex Checker\")\n",
    "print(\"-\" * 30)\n",
    "test_text = \"Contact <NAME_EMAIL> or call ************. Our IP is ***********\"\n",
    "email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'\n",
    "\n",
    "regex_result = regex_checker(email_pattern, test_text)\n",
    "print(f\"Pattern: {email_pattern}\")\n",
    "print(f\"Text: {test_text}\")\n",
    "print(f\"Matches found: {regex_result['matches_found']}\")\n",
    "print(f\"Matches: {regex_result['matches']}\")\n",
    "\n",
    "# Test 2: VirusTotal Lookup (using a known safe IP)\n",
    "print(\"\\n2. Testing VirusTotal Lookup\")\n",
    "print(\"-\" * 30)\n",
    "test_ip = \"*******\"  # Google DNS - generally safe for testing\n",
    "vt_result = virustotal_lookup(test_ip, \"ip\")\n",
    "print(f\"IP: {test_ip}\")\n",
    "if vt_result['success']:\n",
    "    print(f\"Reputation: {vt_result['reputation']}\")\n",
    "    print(f\"Malicious detections: {vt_result['malicious']}\")\n",
    "    print(f\"Harmless detections: {vt_result['harmless']}\")\n",
    "    print(f\"Country: {vt_result['country']}\")\n",
    "else:\n",
    "    print(f\"Error: {vt_result['error']}\")\n",
    "\n",
    "# Test 3: AbuseIPDB Lookup\n",
    "print(\"\\n3. Testing AbuseIPDB Lookup\")\n",
    "print(\"-\" * 30)\n",
    "abuse_result = abuseipdb_lookup(test_ip)\n",
    "print(f\"IP: {test_ip}\")\n",
    "if abuse_result['success']:\n",
    "    print(f\"Abuse Confidence: {abuse_result['abuse_confidence']}%\")\n",
    "    print(f\"Country: {abuse_result['country_code']}\")\n",
    "    print(f\"Usage Type: {abuse_result['usage_type']}\")\n",
    "    print(f\"Total Reports: {abuse_result['total_reports']}\")\n",
    "else:\n",
    "    print(f\"Error: {abuse_result['error']}\")\n",
    "\n",
    "# Test 4: Shodan Lookup\n",
    "print(\"\\n4. Testing Shodan Lookup\")\n",
    "print(\"-\" * 30)\n",
    "shodan_result = shodan_lookup(test_ip)\n",
    "print(f\"IP: {test_ip}\")\n",
    "if shodan_result['success']:\n",
    "    print(f\"Organization: {shodan_result['organization']}\")\n",
    "    print(f\"Country: {shodan_result['country']}\")\n",
    "    print(f\"Open Ports: {shodan_result['ports']}\")\n",
    "    print(f\"Hostnames: {shodan_result['hostnames']}\")\n",
    "else:\n",
    "    print(f\"Error: {shodan_result['error']}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Week 2 testing completed!\")"
   ]
  }
  },
  {
   "cell_type": "markdown",
   "id": "week3",
   "metadata": {},
   "source": [
    "---\n",
    "\n",
    "# Week 3: PII Handling Fundamentals\n",
    "\n",
    "## Learning Objectives\n",
    "- Understand the importance of handling Personally Identifiable Information (PII)\n",
    "- Implement regex-based PII detection and masking\n",
    "- Create bidirectional masking/unmasking functionality\n",
    "- Learn about different PII types and their patterns\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### What is PII?\n",
    "Personally Identifiable Information (PII) is any data that could potentially identify a specific individual. This includes:\n",
    "\n",
    "**Direct Identifiers:**\n",
    "- Full names\n",
    "- Social Security Numbers\n",
    "- Driver's license numbers\n",
    "- Passport numbers\n",
    "\n",
    "**Indirect Identifiers:**\n",
    "- Email addresses\n",
    "- Phone numbers\n",
    "- IP addresses\n",
    "- Credit card numbers\n",
    "\n",
    "### Why Mask PII?\n",
    "1. **Privacy Protection**: Prevent unauthorized access to sensitive information\n",
    "2. **Regulatory Compliance**: Meet GDPR, CCPA, HIPAA requirements\n",
    "3. **Security**: Reduce risk of data breaches\n",
    "4. **AI Safety**: Prevent LLMs from learning or exposing sensitive data\n",
    "\n",
    "### Masking Strategies\n",
    "- **Tokenization**: Replace PII with unique tokens\n",
    "- **Redaction**: Remove or black out sensitive information\n",
    "- **Pseudonymization**: Replace with fake but realistic data\n",
    "- **Encryption**: Encrypt sensitive data with reversible keys\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week3-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 3: Install required packages\n",
    "!pip install uuid\n",
    "\n",
    "import re\n",
    "import uuid\n",
    "from typing import Dict, List, Tuple\n",
    "\n",
    "print(\"✅ Week 3 packages installed and imported successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week3-pii-masker",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 3: Complete PII Masker Implementation\n",
    "\n",
    "class PIIMasker:\n",
    "    \"\"\"\n",
    "    A comprehensive PII masking system that can detect, mask, and unmask\n",
    "    various types of personally identifiable information using regex patterns.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        \"\"\"Initialize the PII masker with pattern mappings and priority order.\"\"\"\n",
    "        \n",
    "        # Dictionaries for bidirectional mapping\n",
    "        self.mask_map = {}  # original_value -> placeholder\n",
    "        self.unmask_map = {}  # placeholder -> original_value\n",
    "        \n",
    "        # Define comprehensive regex patterns for different types of PII\n",
    "        self.pii_patterns = {\n",
    "            'email': r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b',\n",
    "            'phone': r'\\b(?:\\+?1[-.]?)?\\(?([0-9]{3})\\)?[-.]?([0-9]{3})[-.]?([0-9]{4})\\b',\n",
    "            'ssn': r'\\b(?!000|666|9\\d{2})\\d{3}[-.]?(?!00)\\d{2}[-.]?(?!0000)\\d{4}\\b',\n",
    "            'credit_card': r'\\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\\b',\n",
    "            'ip_address': r'\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b',\n",
    "            'domain': r'\\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+(com|org|net|edu|gov|mil|int|co|io|ai|tech|info|biz|name|pro)\\b',\n",
    "            'mac_address': r'\\b(?:[0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}\\b',\n",
    "            'url': r'https?://(?:[-\\w.])+(?:[:\\d]+)?(?:/(?:[\\w/_.])*(?:\\?(?:[\\w&=%.])*)?(?:#(?:[\\w.])*)?)?',\n",
    "            'bank_account': r'\\b\\d{8,17}\\b',\n",
    "            'passport': r'\\b[A-Z]{1,2}[0-9]{6,9}\\b',\n",
    "            'license_plate': r'\\b[A-Z]{1,3}[-\\s]?[0-9]{1,4}[-\\s]?[A-Z]{0,3}\\b'\n",
    "        }\n",
    "        \n",
    "        # Define processing priority (more specific patterns first)\n",
    "        self.pattern_priority = [\n",
    "            'email', 'url', 'credit_card', 'ssn', 'phone', \n",
    "            'mac_address', 'ip_address', 'passport', 'bank_account',\n",
    "            'license_plate', 'domain'\n",
    "        ]\n",
    "    \n",
    "    def mask(self, text: str) -> str:\n",
    "        \"\"\"\n",
    "        Mask PII in text with unique placeholders.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Input text containing potential PII\n",
    "            \n",
    "        Returns:\n",
    "            str: Text with PII replaced by unique placeholders\n",
    "        \"\"\"\n",
    "        masked_text = text\n",
    "        \n",
    "        # Process patterns in priority order to handle overlapping matches\n",
    "        for pii_type in self.pattern_priority:\n",
    "            pattern = self.pii_patterns[pii_type]\n",
    "            \n",
    "            # Find all matches for this pattern\n",
    "            matches = re.finditer(pattern, masked_text, re.IGNORECASE)\n",
    "            \n",
    "            # Process matches in reverse order to maintain string positions\n",
    "            matches_list = list(matches)\n",
    "            for match in reversed(matches_list):\n",
    "                pii_value = match.group()\n",
    "                \n",
    "                # Skip if already masked (contains placeholder pattern)\n",
    "                if any(placeholder in pii_value for placeholder in self.unmask_map.keys()):\n",
    "                    continue\n",
    "                \n",
    "                # Check if this PII value has already been masked\n",
    "                if pii_value not in self.mask_map:\n",
    "                    # Generate unique placeholder\n",
    "                    placeholder = f\"<{pii_type}_{str(uuid.uuid4())[:8]}>\"\n",
    "                    \n",
    "                    # Store bidirectional mapping\n",
    "                    self.mask_map[pii_value] = placeholder\n",
    "                    self.unmask_map[placeholder] = pii_value\n",
    "                \n",
    "                # Replace the PII with its placeholder\n",
    "                start, end = match.span()\n",
    "                masked_text = masked_text[:start] + self.mask_map[pii_value] + masked_text[end:]\n",
    "        \n",
    "        return masked_text\n",
    "    \n",
    "    def unmask(self, text: str) -> str:\n",
    "        \"\"\"\n",
    "        Unmask PII placeholders back to original values.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Text containing PII placeholders\n",
    "            \n",
    "        Returns:\n",
    "            str: Text with placeholders replaced by original PII values\n",
    "        \"\"\"\n",
    "        unmasked_text = text\n",
    "        \n",
    "        # Sort placeholders by length (longest first) to prevent partial replacements\n",
    "        sorted_placeholders = sorted(self.unmask_map.keys(), key=len, reverse=True)\n",
    "        \n",
    "        # Replace each placeholder with its original value\n",
    "        for placeholder in sorted_placeholders:\n",
    "            if placeholder in unmasked_text:\n",
    "                original_value = self.unmask_map[placeholder]\n",
    "                unmasked_text = unmasked_text.replace(placeholder, original_value)\n",
    "        \n",
    "        return unmasked_text\n",
    "    \n",
    "    def clear(self):\n",
    "        \"\"\"Clear stored PII mappings to remove all stored associations.\"\"\"\n",
    "        self.mask_map.clear()\n",
    "        self.unmask_map.clear()\n",
    "    \n",
    "    def get_detected_pii(self) -> Dict[str, List[str]]:\n",
    "        \"\"\"\n",
    "        Get a summary of detected PII types and values.\n",
    "        \n",
    "        Returns:\n",
    "            Dict[str, List[str]]: Dictionary mapping PII types to detected values\n",
    "        \"\"\"\n",
    "        pii_summary = {}\n",
    "        \n",
    "        for original_value, placeholder in self.mask_map.items():\n",
    "            # Extract PII type from placeholder\n",
    "            pii_type = placeholder.split('_')[0][1:]  # Remove '<' and get type\n",
    "            \n",
    "            if pii_type not in pii_summary:\n",
    "                pii_summary[pii_type] = []\n",
    "            \n",
    "            pii_summary[pii_type].append(original_value)\n",
    "        \n",
    "        return pii_summary\n",
    "    \n",
    "    def validate_pii_detection(self, text: str) -> Dict[str, int]:\n",
    "        \"\"\"\n",
    "        Validate PII detection by counting matches for each pattern.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Text to analyze\n",
    "            \n",
    "        Returns:\n",
    "            Dict[str, int]: Count of matches for each PII type\n",
    "        \"\"\"\n",
    "        detection_counts = {}\n",
    "        \n",
    "        for pii_type, pattern in self.pii_patterns.items():\n",
    "            matches = re.findall(pattern, text, re.IGNORECASE)\n",
    "            detection_counts[pii_type] = len(matches)\n",
    "        \n",
    "        return detection_counts\n",
    "\n",
    "print(\"✅ PIIMasker class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week3-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 3: Test the PIIMasker with comprehensive examples\n",
    "\n",
    "print(\"🔒 Testing PII Masking System\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Create an instance of PIIMasker\n",
    "pii_masker = PIIMasker()\n",
    "\n",
    "# Define comprehensive test text with various PII types\n",
    "test_text = \"\"\"\n",
    "Customer Information:\n",
    "Name: John Doe\n",
    "Email: <EMAIL>\n",
    "Phone: +****************\n",
    "SSN: ***********\n",
    "Credit Card: 4532-1234-5678-9012\n",
    "IP Address: ***********00\n",
    "Website: https://www.example.com\n",
    "Bank Account: ****************\n",
    "MAC Address: 00:1B:44:11:3A:B7\n",
    "Passport: *********\n",
    "\n",
    "Please contact our support <NAME_EMAIL> or visit our site at https://support.company.com/help\n",
    "Our office IP is *********** and backup server is at ************\n",
    "\"\"\"\n",
    "\n",
    "print(\"Original Text:\")\n",
    "print(\"-\" * 40)\n",
    "print(test_text)\n",
    "\n",
    "# Validate PII detection before masking\n",
    "print(\"\\nPII Detection Analysis:\")\n",
    "print(\"-\" * 40)\n",
    "detection_counts = pii_masker.validate_pii_detection(test_text)\n",
    "for pii_type, count in detection_counts.items():\n",
    "    if count > 0:\n",
    "        print(f\"{pii_type.capitalize()}: {count} detected\")\n",
    "\n",
    "# Mask the PII\n",
    "masked_text = pii_masker.mask(test_text)\n",
    "\n",
    "print(\"\\nMasked Text:\")\n",
    "print(\"-\" * 40)\n",
    "print(masked_text)\n",
    "\n",
    "# Show detected PII summary\n",
    "print(\"\\nDetected PII Summary:\")\n",
    "print(\"-\" * 40)\n",
    "pii_summary = pii_masker.get_detected_pii()\n",
    "for pii_type, values in pii_summary.items():\n",
    "    print(f\"{pii_type.capitalize()}: {len(values)} items\")\n",
    "    for value in values:\n",
    "        print(f\"  - {value}\")\n",
    "\n",
    "# Unmask the text\n",
    "unmasked_text = pii_masker.unmask(masked_text)\n",
    "\n",
    "print(\"\\nUnmasked Text:\")\n",
    "print(\"-\" * 40)\n",
    "print(unmasked_text)\n",
    "\n",
    "# Verify that unmasking worked correctly\n",
    "print(\"\\nVerification:\")\n",
    "print(\"-\" * 40)\n",
    "if test_text.strip() == unmasked_text.strip():\n",
    "    print(\"✅ Masking and unmasking successful - original text restored!\")\n",
    "else:\n",
    "    print(\"❌ Error: Original text not fully restored\")\n",
    "    print(\"Differences found in the restoration process\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Week 3 PII Masking testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week3-advanced-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 3: Advanced PII Testing - Edge Cases and Complex Scenarios\n",
    "\n",
    "print(\"🔍 Advanced PII Testing - Edge Cases\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Test edge cases and complex scenarios\n",
    "edge_cases = [\n",
    "    {\n",
    "        'name': 'Multiple emails in one sentence',\n",
    "        'text': 'Contact admin@company.<NAME_EMAIL> for assistance'\n",
    "    },\n",
    "    {\n",
    "        'name': 'Mixed PII types',\n",
    "        'text': 'Call 555-0123 <NAME_EMAIL> from IP ********'\n",
    "    },\n",
    "    {\n",
    "        'name': 'International phone formats',\n",
    "        'text': 'US: +1-************, UK: +44-20-7946-0958'\n",
    "    },\n",
    "    {\n",
    "        'name': 'Credit card with spaces',\n",
    "        'text': 'Card number: 4532 1234 5678 9012 expires 12/25'\n",
    "    },\n",
    "    {\n",
    "        'name': 'URLs with parameters',\n",
    "        'text': 'Visit https://api.example.com/user?id=123&token=abc for details'\n",
    "    }\n",
    "]\n",
    "\n",
    "for i, test_case in enumerate(edge_cases, 1):\n",
    "    print(f\"\\nTest Case {i}: {test_case['name']}\")\n",
    "    print(\"-\" * 30)\n",
    "    \n",
    "    # Create fresh masker for each test\n",
    "    test_masker = PIIMasker()\n",
    "    \n",
    "    original = test_case['text']\n",
    "    masked = test_masker.mask(original)\n",
    "    unmasked = test_masker.unmask(masked)\n",
    "    \n",
    "    print(f\"Original:  {original}\")\n",
    "    print(f\"Masked:    {masked}\")\n",
    "    print(f\"Unmasked:  {unmasked}\")\n",
    "    \n",
    "    # Check if restoration is successful\n",
    "    if original == unmasked:\n",
    "        print(\"Status:    ✅ Success\")\n",
    "    else:\n",
    "        print(\"Status:    ❌ Failed\")\n",
    "    \n",
    "    # Show detected PII\n",
    "    pii_detected = test_masker.get_detected_pii()\n",
    "    if pii_detected:\n",
    "        print(\"Detected:  \", end=\"\")\n",
    "        for pii_type, values in pii_detected.items():\n",
    "            print(f\"{pii_type}({len(values)}) \", end=\"\")\n",
    "        print()\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Advanced PII testing completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week3-homework",
   "metadata": {},
   "source": [
    "## Week 3 Homework Assignment\n",
    "\n",
    "### Tasks\n",
    "1. **Extend PII Patterns**: Add regex patterns for:\n",
    "   - Driver's license numbers (format: ABC123456)\n",
    "   - Bitcoin addresses\n",
    "   - IBAN bank account numbers\n",
    "   - Medical record numbers\n",
    "\n",
    "2. **Performance Testing**: \n",
    "   - Test the masker with a 1000-word document\n",
    "   - Measure processing time\n",
    "   - Identify any performance bottlenecks\n",
    "\n",
    "3. **False Positive Analysis**:\n",
    "   - Create test cases that might trigger false positives\n",
    "   - Analyze and improve pattern accuracy\n",
    "   - Document your findings\n",
    "\n",
    "4. **Security Analysis**:\n",
    "   - Research potential vulnerabilities in PII masking\n",
    "   - Propose improvements to the current implementation\n",
    "   - Consider cryptographic approaches\n",
    "\n",
    "### Deliverables\n",
    "- Enhanced PIIMasker class with additional patterns\n",
    "- Performance test results and analysis\n",
    "- Security assessment report (2 pages)\n",
    "- Test cases demonstrating improved accuracy\n",
    "\n",
    "### Notes for Students\n",
    "- **Regex Complexity**: More complex patterns may impact performance\n",
    "- **Cultural Variations**: Consider international formats for phone numbers, addresses\n",
    "- **Context Sensitivity**: Some data is only PII in certain contexts\n",
    "- **Compliance**: Different regulations have different PII definitions\n",
    "\n",
    "---"
   ]
  }


  },
  {
   "cell_type": "markdown",
   "id": "week4",
   "metadata": {},
   "source": [
    "# Week 4: Agent Development\n",
    "\n",
    "## Learning Objectives\n",
    "- Create an Agent class integrated with OpenAI's API\n",
    "- Implement PII masking in the agent's communication pipeline\n",
    "- Add robust error handling and API management\n",
    "- Understand conversation context and message history\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### AI Agent Architecture\n",
    "An AI agent is a software entity that:\n",
    "- **Perceives** its environment through inputs\n",
    "- **Processes** information using AI models\n",
    "- **Acts** based on its understanding\n",
    "- **Learns** from interactions and feedback\n",
    "\n",
    "### OpenAI API Integration\n",
    "Key considerations for production-ready integration:\n",
    "- **Authentication**: Secure API key management\n",
    "- **Rate Limiting**: Respect API usage limits\n",
    "- **Error Handling**: Graceful failure recovery\n",
    "- **Token Management**: Optimize for cost and performance\n",
    "\n",
    "### Security in AI Agents\n",
    "- **Input Sanitization**: Validate and clean user inputs\n",
    "- **Output Filtering**: Prevent sensitive data leakage\n",
    "- **PII Protection**: Mask sensitive information before API calls\n",
    "- **Audit Logging**: Track all interactions for compliance\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week4-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 4: Install and import required packages\n",
    "!pip install openai tiktoken\n",
    "\n",
    "import openai\n",
    "import tiktoken\n",
    "import json\n",
    "import time\n",
    "from datetime import datetime\n",
    "from typing import List, Dict, Any, Optional\n",
    "import logging\n",
    "\n",
    "# Configure logging\n",
    "logging.basicConfig(level=logging.INFO)\n",
    "logger = logging.getLogger(__name__)\n",
    "\n",
    "print(\"✅ Week 4 packages installed and imported successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week4-openai-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 4: Initialize OpenAI client\n",
    "from openai import OpenAI\n",
    "\n",
    "# Initialize the OpenAI client\n",
    "client = OpenAI(api_key=OPENAI_API_KEY)\n",
    "\n",
    "# Test the connection\n",
    "try:\n",
    "    # Make a simple test call\n",
    "    response = client.chat.completions.create(\n",
    "        model=\"gpt-3.5-turbo\",\n",
    "        messages=[{\"role\": \"user\", \"content\": \"Hello, this is a test.\"}],\n",
    "        max_tokens=10\n",
    "    )\n",
    "    print(\"✅ OpenAI API connection successful!\")\n",
    "    print(f\"Test response: {response.choices[0].message.content}\")\n",
    "except Exception as e:\n",
    "    print(f\"❌ OpenAI API connection failed: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week4-agent-class",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 4: Complete Agent Class Implementation\n",
    "\n",
    "class Agent:\n",
    "    \"\"\"\n",
    "    A cybersecurity-focused AI agent with integrated PII protection,\n",
    "    conversation management, and robust error handling.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, \n",
    "                 system: str = \"\", \n",
    "                 model: str = \"gpt-4o\",\n",
    "                 max_tokens: int = 1000,\n",
    "                 temperature: float = 0.1):\n",
    "        \"\"\"\n",
    "        Initialize the agent with system message and PII masking support.\n",
    "        \n",
    "        Args:\n",
    "            system (str): System message to set agent behavior\n",
    "            model (str): OpenAI model to use\n",
    "            max_tokens (int): Maximum tokens per response\n",
    "            temperature (float): Response randomness (0.0-1.0)\n",
    "        \"\"\"\n",
    "        self.system_message = system\n",
    "        self.model = model\n",
    "        self.max_tokens = max_tokens\n",
    "        self.temperature = temperature\n",
    "        \n",
    "        # Initialize message history\n",
    "        self.messages = []\n",
    "        \n",
    "        # Initialize PII masker\n",
    "        self.pii_masker = PIIMasker()\n",
    "        \n",
    "        # Initialize OpenAI client\n",
    "        self.client = OpenAI(api_key=OPENAI_API_KEY)\n",
    "        \n",
    "        # Token counting for cost management\n",
    "        try:\n",
    "            self.encoding = tiktoken.encoding_for_model(model)\n",
    "        except KeyError:\n",
    "            self.encoding = tiktoken.get_encoding(\"cl100k_base\")\n",
    "        \n",
    "        # Add system message if provided\n",
    "        if system:\n",
    "            self.messages.append({\n",
    "                \"role\": \"system\",\n",
    "                \"content\": system,\n",
    "                \"timestamp\": datetime.now().isoformat()\n",
    "            })\n",
    "        \n",
    "        # Statistics tracking\n",
    "        self.stats = {\n",
    "            \"total_calls\": 0,\n",
    "            \"total_tokens\": 0,\n",
    "            \"total_cost\": 0.0,\n",
    "            \"errors\": 0,\n",
    "            \"pii_detections\": 0\n",
    "        }\n",
    "        \n",
    "        logger.info(f\"Agent initialized with model: {model}\")\n",
    "    \n",
    "    def count_tokens(self, text: str) -> int:\n",
    "        \"\"\"\n",
    "        Count tokens in text for cost estimation.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Text to count tokens for\n",
    "            \n",
    "        Returns:\n",
    "            int: Number of tokens\n",
    "        \"\"\"\n",
    "        return len(self.encoding.encode(text))\n",
    "    \n",
    "    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:\n",
    "        \"\"\"\n",
    "        Estimate cost based on token usage.\n",
    "        \n",
    "        Args:\n",
    "            prompt_tokens (int): Input tokens\n",
    "            completion_tokens (int): Output tokens\n",
    "            \n",
    "        Returns:\n",
    "            float: Estimated cost in USD\n",
    "        \"\"\"\n",
    "        # GPT-4 pricing (as of 2024)\n",
    "        if \"gpt-4\" in self.model:\n",
    "            input_cost = prompt_tokens * 0.00003  # $0.03 per 1K tokens\n",
    "            output_cost = completion_tokens * 0.00006  # $0.06 per 1K tokens\n",
    "        else:  # GPT-3.5-turbo\n",
    "            input_cost = prompt_tokens * 0.0000015  # $0.0015 per 1K tokens\n",
    "            output_cost = completion_tokens * 0.000002  # $0.002 per 1K tokens\n",
    "        \n",
    "        return input_cost + output_cost\n",
    "    \n",
    "    def __call__(self, message: str = \"\") -> str:\n",
    "        \"\"\"\n",
    "        Process a user message with PII protection and return AI response.\n",
    "        \n",
    "        Args:\n",
    "            message (str): User input message\n",
    "            \n",
    "        Returns:\n",
    "            str: AI agent response with PII unmasked\n",
    "        \"\"\"\n",
    "        if not message:\n",
    "            return \"Please provide a message for me to process.\"\n",
    "        \n",
    "        try:\n",
    "            # Step 1: Mask PII in user message\n",
    "            masked_message = self.pii_masker.mask(message)\n",
    "            \n",
    "            # Track PII detections\n",
    "            pii_detected = self.pii_masker.get_detected_pii()\n",
    "            if pii_detected:\n",
    "                self.stats[\"pii_detections\"] += sum(len(values) for values in pii_detected.values())\n",
    "                logger.info(f\"PII detected and masked: {list(pii_detected.keys())}\")\n",
    "            \n",
    "            # Step 2: Add masked message to conversation history\n",
    "            self.messages.append({\n",
    "                \"role\": \"user\",\n",
    "                \"content\": masked_message,\n",
    "                \"timestamp\": datetime.now().isoformat(),\n",
    "                \"original_length\": len(message),\n",
    "                \"masked_length\": len(masked_message)\n",
    "            })\n",
    "            \n",
    "            # Step 3: Get AI response\n",
    "            ai_response = self.execute()\n",
    "            \n",
    "            # Step 4: Unmask PII in response\n",
    "            unmasked_response = self.pii_masker.unmask(ai_response)\n",
    "            \n",
    "            # Step 5: Add response to conversation history\n",
    "            self.messages.append({\n",
    "                \"role\": \"assistant\",\n",
    "                \"content\": unmasked_response,\n",
    "                \"timestamp\": datetime.now().isoformat(),\n",
    "                \"model\": self.model\n",
    "            })\n",
    "            \n",
    "            return unmasked_response\n",
    "            \n",
    "        except Exception as e:\n",
    "            self.stats[\"errors\"] += 1\n",
    "            error_msg = f\"Agent error: {str(e)}\"\n",
    "            logger.error(error_msg)\n",
    "            return f\"I apologize, but I encountered an error processing your request: {error_msg}\"\n",
    "    \n",
    "    def execute(self) -> str:\n",
    "        \"\"\"\n",
    "        Send messages to OpenAI's API and retrieve the response.\n",
    "        \n",
    "        Returns:\n",
    "            str: AI model response\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Prepare messages for API call (exclude metadata)\n",
    "            api_messages = []\n",
    "            for msg in self.messages:\n",
    "                api_messages.append({\n",
    "                    \"role\": msg[\"role\"],\n",
    "                    \"content\": msg[\"content\"]\n",
    "                })\n",
    "            \n",
    "            # Count input tokens\n",
    "            prompt_text = \"\\n\".join([msg[\"content\"] for msg in api_messages])\n",
    "            prompt_tokens = self.count_tokens(prompt_text)\n",
    "            \n",
    "            # Make API call with retry logic\n",
    "            max_retries = 3\n",
    "            for attempt in range(max_retries):\n",
    "                try:\n",
    "                    response = self.client.chat.completions.create(\n",
    "                        model=self.model,\n",
    "                        messages=api_messages,\n",
    "                        max_tokens=self.max_tokens,\n",
    "                        temperature=self.temperature\n",
    "                    )\n",
    "                    break\n",
    "                except openai.RateLimitError as e:\n",
    "                    if attempt < max_retries - 1:\n",
    "                        wait_time = 2 ** attempt  # Exponential backoff\n",
    "                        logger.warning(f\"Rate limit hit, waiting {wait_time}s before retry {attempt + 1}\")\n",
    "                        time.sleep(wait_time)\n",
    "                    else:\n",
    "                        raise e\n",
    "            \n",
    "            # Extract response content\n",
    "            content = response.choices[0].message.content\n",
    "            \n",
    "            # Update statistics\n",
    "            self.stats[\"total_calls\"] += 1\n",
    "            completion_tokens = response.usage.completion_tokens\n",
    "            total_tokens = response.usage.total_tokens\n",
    "            self.stats[\"total_tokens\"] += total_tokens\n",
    "            \n",
    "            # Calculate and track cost\n",
    "            cost = self.estimate_cost(prompt_tokens, completion_tokens)\n",
    "            self.stats[\"total_cost\"] += cost\n",
    "            \n",
    "            logger.info(f\"API call successful. Tokens: {total_tokens}, Cost: ${cost:.6f}\")\n",
    "            \n",
    "            return content\n",
    "            \n",
    "        except openai.APIError as e:\n",
    "            error_msg = f\"OpenAI API error: {str(e)}\"\n",
    "            logger.error(error_msg)\n",
    "            raise Exception(error_msg)\n",
    "        except Exception as e:\n",
    "            error_msg = f\"Unexpected error during API call: {str(e)}\"\n",
    "            logger.error(error_msg)\n",
    "            raise Exception(error_msg)\n",
    "    \n",
    "    def clear_conversation(self):\n",
    "        \"\"\"\n",
    "        Clear conversation history while preserving system message.\n",
    "        \"\"\"\n",
    "        # Keep only system message if it exists\n",
    "        system_messages = [msg for msg in self.messages if msg[\"role\"] == \"system\"]\n",
    "        self.messages = system_messages\n",
    "        \n",
    "        # Clear PII mappings\n",
    "        self.pii_masker.clear()\n",
    "        \n",
    "        logger.info(\"Conversation history cleared\")\n",
    "    \n",
    "    def get_conversation_history(self) -> List[Dict[str, Any]]:\n",
    "        \"\"\"\n",
    "        Get the full conversation history with metadata.\n",
    "        \n",
    "        Returns:\n",
    "            List[Dict[str, Any]]: Complete conversation history\n",
    "        \"\"\"\n",
    "        return self.messages.copy()\n",
    "    \n",
    "    def get_statistics(self) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get agent usage statistics.\n",
    "        \n",
    "        Returns:\n",
    "            Dict[str, Any]: Usage statistics\n",
    "        \"\"\"\n",
    "        return self.stats.copy()\n",
    "    \n",
    "    def export_conversation(self, filename: str = None) -> str:\n",
    "        \"\"\"\n",
    "        Export conversation history to JSON file.\n",
    "        \n",
    "        Args:\n",
    "            filename (str): Output filename\n",
    "            \n",
    "        Returns:\n",
    "            str: Filename of exported conversation\n",
    "        \"\"\"\n",
    "        if not filename:\n",
    "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "            filename = f\"conversation_{timestamp}.json\"\n",
    "        \n",
    "        export_data = {\n",
    "            \"agent_config\": {\n",
    "                \"model\": self.model,\n",
    "                \"max_tokens\": self.max_tokens,\n",
    "                \"temperature\": self.temperature\n",
    "            },\n",
    "            \"conversation\": self.messages,\n",
    "            \"statistics\": self.stats,\n",
    "            \"exported_at\": datetime.now().isoformat()\n",
    "        }\n",
    "        \n",
    "        with open(filename, 'w', encoding='utf-8') as f:\n",
    "            json.dump(export_data, f, indent=2, ensure_ascii=False)\n",
    "        \n",
    "        logger.info(f\"Conversation exported to {filename}\")\n",
    "        return filename\n",
    "\n",
    "print(\"✅ Agent class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week4-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 4: Test the Agent Implementation\n",
    "\n",
    "print(\"🤖 Testing AI Agent with PII Protection\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Define a comprehensive system prompt for cybersecurity\n",
    "system_prompt = \"\"\"\n",
    "You are CyberShield AI, a cybersecurity assistant designed to help with:\n",
    "1. Threat analysis and risk assessment\n",
    "2. Security best practices and recommendations\n",
    "3. Incident response guidance\n",
    "4. Compliance and regulatory advice\n",
    "\n",
    "Guidelines:\n",
    "- Always prioritize security and privacy\n",
    "- Provide clear, actionable recommendations\n",
    "- Explain technical concepts in accessible terms\n",
    "- Never store or expose sensitive information\n",
    "- Ask for clarification when needed\n",
    "\n",
    "Remember: You are designed to protect sensitive information while providing expert cybersecurity guidance.\n",
    "\"\"\"\n",
    "\n",
    "# Initialize the agent with the system prompt\n",
    "agent = Agent(\n",
    "    system=system_prompt,\n",
    "    model=\"gpt-4o\",\n",
    "    max_tokens=500,\n",
    "    temperature=0.1\n",
    ")\n",
    "\n",
    "print(\"Agent initialized with cybersecurity system prompt\")\n",
    "print(f\"Model: {agent.model}\")\n",
    "print(f\"Max tokens: {agent.max_tokens}\")\n",
    "print(f\"Temperature: {agent.temperature}\")\n",
    "\n",
    "# Test message with PII\n",
    "test_message = \"\"\"\n",
    "I'm concerned about a security incident. Someone accessed our system from IP address ************ \n",
    "and may have compromised the <NAME_EMAIL>. They also tried to access \n",
    "our database server at ***********00. The incident happened when our employee John Smith \n",
    "(phone: ************) was working remotely. What should we do?\n",
    "\"\"\"\n",
    "\n",
    "print(\"\\nTest Message (contains PII):\")\n",
    "print(\"-\" * 40)\n",
    "print(test_message)\n",
    "\n",
    "# Process the message through the agent\n",
    "print(\"\\nProcessing through agent...\")\n",
    "response = agent(test_message)\n",
    "\n",
    "print(\"\\nAgent Response:\")\n",
    "print(\"-\" * 40)\n",
    "print(response)\n",
    "\n",
    "# Show PII detection summary\n",
    "print(\"\\nPII Detection Summary:\")\n",
    "print(\"-\" * 40)\n",
    "pii_summary = agent.pii_masker.get_detected_pii()\n",
    "if pii_summary:\n",
    "    for pii_type, values in pii_summary.items():\n",
    "        print(f\"{pii_type.capitalize()}: {len(values)} detected\")\n",
    "        for value in values:\n",
    "            print(f\"  - {value}\")\n",
    "else:\n",
    "    print(\"No PII detected\")\n",
    "\n",
    "# Show agent statistics\n",
    "print(\"\\nAgent Statistics:\")\n",
    "print(\"-\" * 40)\n",
    "stats = agent.get_statistics()\n",
    "for key, value in stats.items():\n",
    "    if key == \"total_cost\":\n",
    "        print(f\"{key}: ${value:.6f}\")\n",
    "    else:\n",
    "        print(f\"{key}: {value}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Week 4 Agent testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week4-advanced-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 4: Advanced Agent Testing - Multiple Interactions\n",
    "\n",
    "print(\"🔄 Advanced Agent Testing - Conversation Flow\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Test multiple interactions to verify conversation context\n",
    "test_conversations = [\n",
    "    \"What are the key steps in incident response?\",\n",
    "    \"How should I secure the <NAME_EMAIL> that was compromised?\",\n",
    "    \"What about the IP ************ that was involved in the attack?\",\n",
    "    \"Can you summarize the security recommendations you've provided?\"\n",
    "]\n",
    "\n",
    "for i, message in enumerate(test_conversations, 1):\n",
    "    print(f\"\\nInteraction {i}:\")\n",
    "    print(\"-\" * 20)\n",
    "    print(f\"User: {message}\")\n",
    "    \n",
    "    response = agent(message)\n",
    "    print(f\"Agent: {response[:200]}{'...' if len(response) > 200 else ''}\")\n",
    "    \n",
    "    # Show token usage for this interaction\n",
    "    current_stats = agent.get_statistics()\n",
    "    print(f\"Tokens used: {current_stats['total_tokens']}\")\n",
    "    print(f\"Cost so far: ${current_stats['total_cost']:.6f}\")\n",
    "\n",
    "# Test conversation export\n",
    "print(\"\\nExporting conversation...\")\n",
    "export_file = agent.export_conversation()\n",
    "print(f\"Conversation exported to: {export_file}\")\n",
    "\n",
    "# Test conversation clearing\n",
    "print(\"\\nClearing conversation...\")\n",
    "agent.clear_conversation()\n",
    "print(\"Conversation cleared. Testing with new message...\")\n",
    "\n",
    "# Test after clearing\n",
    "new_response = agent(\"Hello, I'm a new user. Can you help me with cybersecurity?\")\n",
    "print(f\"New conversation response: {new_response[:100]}...\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Advanced agent testing completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week4-homework",
   "metadata": {},
   "source": [
    "## Week 4 Homework Assignment\n",
    "\n",
    "### Tasks\n",
    "1. **Error Handling Enhancement**:\n",
    "   - Implement retry logic with exponential backoff\n",
    "   - Add handling for different OpenAI API errors\n",
    "   - Create fallback responses for API failures\n",
    "\n",
    "2. **Conversation Management**:\n",
    "   - Implement conversation summarization for long chats\n",
    "   - Add conversation branching capabilities\n",
    "   - Create conversation templates for common scenarios\n",
    "\n",
    "3. **Performance Optimization**:\n",
    "   - Implement token counting and cost tracking\n",
    "   - Add conversation compression techniques\n",
    "   - Optimize API calls for better response times\n",
    "\n",
    "4. **Security Enhancements**:\n",
    "   - Add input validation and sanitization\n",
    "   - Implement audit logging for all interactions\n",
    "   - Create security policies for different user roles\n",
    "\n",
    "### Deliverables\n",
    "- Enhanced Agent class with improved error handling\n",
    "- Conversation management system\n",
    "- Performance benchmarking results\n",
    "- Security assessment and implementation plan\n",
    "\n",
    "### Notes for Students\n",
    "- **API Costs**: Monitor token usage to control costs\n",
    "- **Rate Limits**: Implement proper rate limiting to avoid API errors\n",
    "- **Context Windows**: Be aware of model context limitations\n",
    "- **Security**: Never log or store actual PII values\n",
    "\n",
    "---"
   ]
  }


  },
  {
   "cell_type": "markdown",
   "id": "week5",
   "metadata": {},
   "source": [
    "# Week 5: Advanced PII Protection with SpaCy\n",
    "\n",
    "## Learning Objectives\n",
    "- Enhance PII detection using SpaCy's Natural Language Processing\n",
    "- Implement Named Entity Recognition (NER) for contextual PII detection\n",
    "- Create custom entity recognition components\n",
    "- Optimize performance for large-scale PII detection\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### Natural Language Processing for PII Detection\n",
    "While regex patterns are excellent for structured data, NLP provides:\n",
    "- **Contextual Understanding**: Recognizes entities based on surrounding text\n",
    "- **Flexible Matching**: Handles variations in formatting and language\n",
    "- **Semantic Analysis**: Understands meaning beyond pattern matching\n",
    "- **Multi-language Support**: Works across different languages\n",
    "\n",
    "### SpaCy Named Entity Recognition\n",
    "SpaCy's NER system can identify:\n",
    "- **PERSON**: People's names\n",
    "- **ORG**: Organizations and companies\n",
    "- **GPE**: Geopolitical entities (countries, cities)\n",
    "- **MONEY**: Monetary values\n",
    "- **DATE/TIME**: Temporal expressions\n",
    "- **NORP**: Nationalities and religious groups\n",
    "\n",
    "### Hybrid Approach Benefits\n",
    "Combining regex and NLP provides:\n",
    "- **High Precision**: Regex for structured patterns\n",
    "- **High Recall**: NLP for contextual entities\n",
    "- **Comprehensive Coverage**: Best of both approaches\n",
    "- **Reduced False Positives**: Cross-validation between methods\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week5-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 5: Install and setup SpaCy\n",
    "!pip install spacy\n",
    "!python -m spacy download en_core_web_lg\n",
    "\n",
    "import spacy\n",
    "from spacy.matcher import Matcher\n",
    "from spacy.tokens import Span\n",
    "import uuid\n",
    "from typing import Dict, List, Set, Tuple\n",
    "import time\n",
    "\n",
    "print(\"✅ SpaCy installed and language model downloaded!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week5-enhanced-masker",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 5: Enhanced PII Masker with SpaCy Integration\n",
    "\n",
    "class EnhancedPIIMasker(PIIMasker):\n",
    "    \"\"\"\n",
    "    Advanced PII masker that combines regex patterns with SpaCy's NLP\n",
    "    capabilities for comprehensive PII detection and protection.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, model_name: str = \"en_core_web_lg\"):\n",
    "        \"\"\"\n",
    "        Initialize enhanced PII masker with SpaCy NLP model.\n",
    "        \n",
    "        Args:\n",
    "            model_name (str): SpaCy model to use for NLP processing\n",
    "        \"\"\"\n",
    "        # Initialize parent class\n",
    "        super().__init__()\n",
    "        \n",
    "        # Load SpaCy model\n",
    "        try:\n",
    "            self.nlp = spacy.load(model_name)\n",
    "            print(f\"✅ Loaded SpaCy model: {model_name}\")\n",
    "        except OSError:\n",
    "            print(f\"❌ Model {model_name} not found. Falling back to en_core_web_sm\")\n",
    "            self.nlp = spacy.load(\"en_core_web_sm\")\n",
    "        \n",
    "        # SpaCy entity types that may contain PII\n",
    "        self.spacy_pii_types = {\n",
    "            'PERSON': 'person',\n",
    "            'ORG': 'organization', \n",
    "            'GPE': 'location',\n",
    "            'MONEY': 'money',\n",
    "            'DATE': 'date',\n",
    "            'TIME': 'time',\n",
    "            'NORP': 'nationality',\n",
    "            'FAC': 'facility',\n",
    "            'LOC': 'location',\n",
    "            'PRODUCT': 'product',\n",
    "            'EVENT': 'event',\n",
    "            'WORK_OF_ART': 'work_of_art',\n",
    "            'LAW': 'law',\n",
    "            'LANGUAGE': 'language'\n",
    "        }\n",
    "        \n",
    "        # Initialize custom matcher for additional patterns\n",
    "        self.matcher = Matcher(self.nlp.vocab)\n",
    "        self._add_custom_patterns()\n",
    "        \n",
    "        # Add custom pipeline component\n",
    "        if \"custom_pii_detector\" not in self.nlp.pipe_names:\n",
    "            self.nlp.add_pipe(\"custom_pii_detector\", last=True)\n",
    "        \n",
    "        # Configuration options\n",
    "        self.use_combined_approach = True\n",
    "        self.confidence_threshold = 0.7\n",
    "        \n",
    "        # Performance tracking\n",
    "        self.performance_stats = {\n",
    "            'regex_detections': 0,\n",
    "            'spacy_detections': 0,\n",
    "            'total_processing_time': 0.0,\n",
    "            'documents_processed': 0\n",
    "        }\n",
    "    \n",
    "    def _add_custom_patterns(self):\n",
    "        \"\"\"\n",
    "        Add custom patterns to the SpaCy matcher for specialized PII detection.\n",
    "        \"\"\"\n",
    "        # Account number patterns\n",
    "        account_pattern = [{\"TEXT\": {\"REGEX\": r\"\\d{8,17}\"}}]\n",
    "        self.matcher.add(\"ACCOUNT_NUMBER\", [account_pattern])\n",
    "        \n",
    "        # License plate patterns\n",
    "        license_pattern = [{\"TEXT\": {\"REGEX\": r\"[A-Z]{1,3}[-\\s]?[0-9]{1,4}[-\\s]?[A-Z]{0,3}\"}}]\n",
    "        self.matcher.add(\"LICENSE_PLATE\", [license_pattern])\n",
    "        \n",
    "        # Employee ID patterns\n",
    "        employee_id_pattern = [\n",
    "            {\"LOWER\": {\"IN\": [\"employee\", \"emp\", \"staff\"]}},\n",
    "            {\"LOWER\": {\"IN\": [\"id\", \"number\", \"#\"]}, \"OP\": \"?\"},\n",
    "            {\"TEXT\": {\"REGEX\": r\"[A-Z0-9]{4,10}\"}}\n",
    "        ]\n",
    "        self.matcher.add(\"EMPLOYEE_ID\", [employee_id_pattern])\n",
    "        \n",
    "        # Medical record number patterns\n",
    "        mrn_pattern = [\n",
    "            {\"LOWER\": {\"IN\": [\"mrn\", \"medical\", \"patient\"]}},\n",
    "            {\"LOWER\": {\"IN\": [\"record\", \"id\", \"number\"]}, \"OP\": \"?\"},\n",
    "            {\"TEXT\": {\"REGEX\": r\"[0-9]{6,12}\"}}\n",
    "        ]\n",
    "        self.matcher.add(\"MEDICAL_RECORD\", [mrn_pattern])\n",
    "    \n",
    "    @spacy.Language.component(\"custom_pii_detector\")\n",
    "    def custom_pii_detector(self, doc):\n",
    "        \"\"\"\n",
    "        Custom pipeline component for additional PII detection.\n",
    "        \n",
    "        Args:\n",
    "            doc: SpaCy document object\n",
    "            \n",
    "        Returns:\n",
    "            doc: Modified document with additional entities\n",
    "        \"\"\"\n",
    "        # Find matches using custom patterns\n",
    "        matches = self.matcher(doc)\n",
    "        \n",
    "        # Create new entities from matches\n",
    "        new_entities = []\n",
    "        for match_id, start, end in matches:\n",
    "            # Get the matched span\n",
    "            span = doc[start:end]\n",
    "            \n",
    "            # Get the label name\n",
    "            label = self.nlp.vocab.strings[match_id]\n",
    "            \n",
    "            # Create new entity\n",
    "            new_entity = Span(doc, start, end, label=label)\n",
    "            new_entities.append(new_entity)\n",
    "        \n",
    "        # Add new entities to existing ones\n",
    "        doc.ents = list(doc.ents) + new_entities\n",
    "        \n",
    "        return doc\n",
    "    \n",
    "    def mask_with_spacy(self, text: str) -> str:\n",
    "        \"\"\"\n",
    "        Mask PII using SpaCy's named entity recognition.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Input text to process\n",
    "            \n",
    "        Returns:\n",
    "            str: Text with SpaCy-detected PII masked\n",
    "        \"\"\"\n",
    "        start_time = time.time()\n",
    "        \n",
    "        # Process text with SpaCy\n",
    "        doc = self.nlp(text)\n",
    "        masked_text = text\n",
    "        \n",
    "        # Sort entities by position (reverse order to maintain indices)\n",
    "        entities = sorted(doc.ents, key=lambda x: x.start_char, reverse=True)\n",
    "        \n",
    "        spacy_detections = 0\n",
    "        \n",
    "        for ent in entities:\n",
    "            # Check if entity type is considered PII\n",
    "            if ent.label_ in self.spacy_pii_types or ent.label_ in ['ACCOUNT_NUMBER', 'LICENSE_PLATE', 'EMPLOYEE_ID', 'MEDICAL_RECORD']:\n",
    "                pii_value = ent.text.strip()\n",
    "                \n",
    "                # Skip if already masked or too short\n",
    "                if len(pii_value) < 2 or any(placeholder in pii_value for placeholder in self.unmask_map.keys()):\n",
    "                    continue\n",
    "                \n",
    "                # Skip common words that might be false positives\n",
    "                if pii_value.lower() in ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']:\n",
    "                    continue\n",
    "                \n",
    "                # Create placeholder if not already masked\n",
    "                if pii_value not in self.mask_map:\n",
    "                    if ent.label_ in self.spacy_pii_types:\n",
    "                        pii_type = self.spacy_pii_types[ent.label_]\n",
    "                    else:\n",
    "                        pii_type = ent.label_.lower()\n",
    "                    \n",
    "                    placeholder = f\"<{pii_type}_{str(uuid.uuid4())[:8]}>\"\n",
    "                    self.mask_map[pii_value] = placeholder\n",
    "                    self.unmask_map[placeholder] = pii_value\n",
    "                    spacy_detections += 1\n",
    "                \n",
    "                # Replace in text using character positions\n",
    "                masked_text = masked_text[:ent.start_char] + self.mask_map[pii_value] + masked_text[ent.end_char:]\n",
    "        \n",
    "        # Update performance stats\n",
    "        processing_time = time.time() - start_time\n",
    "        self.performance_stats['spacy_detections'] += spacy_detections\n",
    "        self.performance_stats['total_processing_time'] += processing_time\n",
    "        \n",
    "        return masked_text\n",
    "    \n",
    "    def mask(self, text: str) -> str:\n",
    "        \"\"\"\n",
    "        Enhanced masking using both regex and SpaCy approaches.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Input text to mask\n",
    "            \n",
    "        Returns:\n",
    "            str: Text with all detected PII masked\n",
    "        \"\"\"\n",
    "        start_time = time.time()\n",
    "        \n",
    "        if self.use_combined_approach:\n",
    "            # Step 1: Apply regex-based masking first\n",
    "            masked_text = super().mask(text)\n",
    "            regex_detections = len(self.mask_map)\n",
    "            \n",
    "            # Step 2: Apply SpaCy-based masking\n",
    "            masked_text = self.mask_with_spacy(masked_text)\n",
    "            \n",
    "            # Update stats\n",
    "            total_detections = len(self.mask_map)\n",
    "            self.performance_stats['regex_detections'] += regex_detections\n",
    "            \n",
    "        else:\n",
    "            # Use only regex-based masking\n",
    "            masked_text = super().mask(text)\n",
    "            self.performance_stats['regex_detections'] += len(self.mask_map)\n",
    "        \n",
    "        # Update document count\n",
    "        self.performance_stats['documents_processed'] += 1\n",
    "        \n",
    "        return masked_text\n",
    "    \n",
    "    def set_masking_approach(self, use_combined: bool = True):\n",
    "        \"\"\"\n",
    "        Configure whether to use combined approach or just regex.\n",
    "        \n",
    "        Args:\n",
    "            use_combined (bool): Whether to use both regex and SpaCy\n",
    "        \"\"\"\n",
    "        self.use_combined_approach = use_combined\n",
    "        print(f\"Masking approach set to: {'Combined (Regex + SpaCy)' if use_combined else 'Regex only'}\")\n",
    "    \n",
    "    def analyze_text_entities(self, text: str) -> Dict[str, List[Dict[str, any]]]:\n",
    "        \"\"\"\n",
    "        Analyze text and return detailed entity information.\n",
    "        \n",
    "        Args:\n",
    "            text (str): Text to analyze\n",
    "            \n",
    "        Returns:\n",
    "            Dict[str, List[Dict[str, any]]]: Detailed entity analysis\n",
    "        \"\"\"\n",
    "        doc = self.nlp(text)\n",
    "        \n",
    "        entity_analysis = {\n",
    "            'spacy_entities': [],\n",
    "            'regex_matches': [],\n",
    "            'summary': {\n",
    "                'total_entities': len(doc.ents),\n",
    "                'pii_entities': 0,\n",
    "                'entity_types': set()\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        # Analyze SpaCy entities\n",
    "        for ent in doc.ents:\n",
    "            entity_info = {\n",
    "                'text': ent.text,\n",
    "                'label': ent.label_,\n",
    "                'start': ent.start_char,\n",
    "                'end': ent.end_char,\n",
    "                'is_pii': ent.label_ in self.spacy_pii_types\n",
    "            }\n",
    "            entity_analysis['spacy_entities'].append(entity_info)\n",
    "            entity_analysis['summary']['entity_types'].add(ent.label_)\n",
    "            \n",
    "            if entity_info['is_pii']:\n",
    "                entity_analysis['summary']['pii_entities'] += 1\n",
    "        \n",
    "        # Analyze regex matches\n",
    "        for pii_type, pattern in self.pii_patterns.items():\n",
    "            matches = list(re.finditer(pattern, text, re.IGNORECASE))\n",
    "            for match in matches:\n",
    "                match_info = {\n",
    "                    'text': match.group(),\n",
    "                    'type': pii_type,\n",
    "                    'start': match.start(),\n",
    "                    'end': match.end(),\n",
    "                    'pattern': pattern\n",
    "                }\n",
    "                entity_analysis['regex_matches'].append(match_info)\n",
    "        \n",
    "        # Convert set to list for JSON serialization\n",
    "        entity_analysis['summary']['entity_types'] = list(entity_analysis['summary']['entity_types'])\n",
    "        \n",
    "        return entity_analysis\n",
    "    \n",
    "    def get_performance_stats(self) -> Dict[str, any]:\n",
    "        \"\"\"\n",
    "        Get performance statistics for the enhanced masker.\n",
    "        \n",
    "        Returns:\n",
    "            Dict[str, any]: Performance statistics\n",
    "        \"\"\"\n",
    "        stats = self.performance_stats.copy()\n",
    "        \n",
    "        if stats['documents_processed'] > 0:\n",
    "            stats['avg_processing_time'] = stats['total_processing_time'] / stats['documents_processed']\n",
    "            stats['avg_detections_per_doc'] = (stats['regex_detections'] + stats['spacy_detections']) / stats['documents_processed']\n",
    "        else:\n",
    "            stats['avg_processing_time'] = 0.0\n",
    "            stats['avg_detections_per_doc'] = 0.0\n",
    "        \n",
    "        return stats\n",
    "\n",
    "print(\"✅ EnhancedPIIMasker class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week5-enhanced-agent",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 5: Enhanced Agent with Advanced PII Protection\n",
    "\n",
    "class EnhancedAgent(Agent):\n",
    "    \"\"\"\n",
    "    Enhanced cybersecurity agent with advanced PII protection using\n",
    "    both regex and NLP-based detection methods.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, \n",
    "                 system: str = \"\",\n",
    "                 model: str = \"gpt-4o\",\n",
    "                 max_tokens: int = 1000,\n",
    "                 temperature: float = 0.1,\n",
    "                 spacy_model: str = \"en_core_web_lg\"):\n",
    "        \"\"\"\n",
    "        Initialize enhanced agent with advanced PII masking.\n",
    "        \n",
    "        Args:\n",
    "            system (str): System message\n",
    "            model (str): OpenAI model to use\n",
    "            max_tokens (int): Maximum tokens per response\n",
    "            temperature (float): Response randomness\n",
    "            spacy_model (str): SpaCy model for NLP processing\n",
    "        \"\"\"\n",
    "        # Initialize parent class but don't create PII masker yet\n",
    "        self.system_message = system\n",
    "        self.model = model\n",
    "        self.max_tokens = max_tokens\n",
    "        self.temperature = temperature\n",
    "        \n",
    "        # Initialize message history\n",
    "        self.messages = []\n",
    "        \n",
    "        # Initialize enhanced PII masker\n",
    "        self.pii_masker = EnhancedPIIMasker(spacy_model)\n",
    "        \n",
    "        # Initialize OpenAI client\n",
    "        self.client = OpenAI(api_key=OPENAI_API_KEY)\n",
    "        \n",
    "        # Token counting\n",
    "        try:\n",
    "            self.encoding = tiktoken.encoding_for_model(model)\n",
    "        except KeyError:\n",
    "            self.encoding = tiktoken.get_encoding(\"cl100k_base\")\n",
    "        \n",
    "        # Add system message if provided\n",
    "        if system:\n",
    "            self.messages.append({\n",
    "                \"role\": \"system\",\n",
    "                \"content\": system,\n",
    "                \"timestamp\": datetime.now().isoformat()\n",
    "            })\n",
    "        \n",
    "        # Enhanced statistics tracking\n",
    "        self.stats = {\n",
    "            \"total_calls\": 0,\n",
    "            \"total_tokens\": 0,\n",
    "            \"total_cost\": 0.0,\n",
    "            \"errors\": 0,\n",
    "            \"pii_detections\": 0,\n",
    "            \"regex_detections\": 0,\n",
    "            \"spacy_detections\": 0,\n",
    "            \"processing_time\": 0.0\n",
    "        }\n",
    "        \n",
    "        logger.info(f\"Enhanced Agent initialized with model: {model} and SpaCy: {spacy_model}\")\n",
    "    \n",
    "    def set_masking_config(self, use_combined: bool = True):\n",
    "        \"\"\"\n",
    "        Configure the PII masking strategy.\n",
    "        \n",
    "        Args:\n",
    "            use_combined (bool): Whether to use combined regex + SpaCy approach\n",
    "        \"\"\"\n",
    "        self.pii_masker.set_masking_approach(use_combined)\n",
    "    \n",
    "    def analyze_message_entities(self, message: str) -> Dict[str, any]:\n",
    "        \"\"\"\n",
    "        Analyze entities in a message before processing.\n",
    "        \n",
    "        Args:\n",
    "            message (str): Message to analyze\n",
    "            \n",
    "        Returns:\n",
    "            Dict[str, any]: Entity analysis results\n",
    "        \"\"\"\n",
    "        return self.pii_masker.analyze_text_entities(message)\n",
    "    \n",
    "    def get_enhanced_statistics(self) -> Dict[str, any]:\n",
    "        \"\"\"\n",
    "        Get comprehensive statistics including PII masking performance.\n",
    "        \n",
    "        Returns:\n",
    "            Dict[str, any]: Enhanced statistics\n",
    "        \"\"\"\n",
    "        # Get base statistics\n",
    "        base_stats = self.get_statistics()\n",
    "        \n",
    "        # Get PII masker performance stats\n",
    "        pii_stats = self.pii_masker.get_performance_stats()\n",
    "        \n",
    "        # Combine statistics\n",
    "        enhanced_stats = {\n",
    "            **base_stats,\n",
    "            \"pii_masking\": pii_stats,\n",
    "            \"masking_approach\": \"Combined\" if self.pii_masker.use_combined_approach else \"Regex only\"\n",
    "        }\n",
    "        \n",
    "        return enhanced_stats\n",
    "\n",
    "print(\"✅ EnhancedAgent class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week5-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 5: Test Enhanced PII Masking with SpaCy\n",
    "\n",
    "print(\"🧠 Testing Enhanced PII Masking with SpaCy\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Create enhanced PII masker\n",
    "enhanced_masker = EnhancedPIIMasker()\n",
    "\n",
    "# Complex test text with various PII types\n",
    "complex_text = \"\"\"\n",
    "Security Incident Report\n",
    "Date: March 15, 2024\n",
    "Reporter: Sarah Johnson from Acme Corporation\n",
    "Contact: <EMAIL>, ******-987-6543\n",
    "\n",
    "Incident Details:\n",
    "An unauthorized access attempt was detected from IP address ************ at 2:30 AM EST.\n",
    "The attacker tried to access our database server located at https://db.internal.acme.com\n",
    "using stolen credentials for employee ID EMP001234.\n",
    "\n",
    "Affected Systems:\n",
    "- Customer database containing credit card 4532-1234-5678-9012\n",
    "- Employee records with SSN ***********\n",
    "- Financial system at ***********00\n",
    "\n",
    "The incident was reported by John Smith (employee #EMP005678) who noticed unusual activity\n",
    "on his workstation with MAC address 00:1B:44:11:3A:B7. The attack originated from\n",
    "New York and targeted our Los Angeles office.\n",
    "\n",
    "Estimated financial impact: $50,000 in potential damages.\n",
    "Patient medical record MRN 987654321 was also accessed.\n",
    "\"\"\"\n",
    "\n",
    "print(\"Original Complex Text:\")\n",
    "print(\"-\" * 40)\n",
    "print(complex_text)\n",
    "\n",
    "# Test entity analysis before masking\n",
    "print(\"\\nEntity Analysis:\")\n",
    "print(\"-\" * 40)\n",
    "entity_analysis = enhanced_masker.analyze_text_entities(complex_text)\n",
    "\n",
    "print(f\"SpaCy Entities Found: {len(entity_analysis['spacy_entities'])}\")\n",
    "for entity in entity_analysis['spacy_entities'][:10]:  # Show first 10\n",
    "    print(f\"  - {entity['text']} ({entity['label']}) - PII: {entity['is_pii']}\")\n",
    "\n",
    "print(f\"\\nRegex Matches Found: {len(entity_analysis['regex_matches'])}\")\n",
    "for match in entity_analysis['regex_matches'][:10]:  # Show first 10\n",
    "    print(f\"  - {match['text']} ({match['type']})\")\n",
    "\n",
    "# Test combined masking approach\n",
    "print(\"\\nTesting Combined Approach (Regex + SpaCy):\")\n",
    "print(\"-\" * 50)\n",
    "enhanced_masker.set_masking_approach(use_combined=True)\n",
    "masked_combined = enhanced_masker.mask(complex_text)\n",
    "print(masked_combined)\n",
    "\n",
    "# Show detected PII summary\n",
    "print(\"\\nDetected PII Summary (Combined):\")\n",
    "print(\"-\" * 40)\n",
    "pii_summary = enhanced_masker.get_detected_pii()\n",
    "for pii_type, values in pii_summary.items():\n",
    "    print(f\"{pii_type.capitalize()}: {len(values)} items\")\n",
    "\n",
    "# Test unmasking\n",
    "unmasked_text = enhanced_masker.unmask(masked_combined)\n",
    "print(\"\\nUnmasking Verification:\")\n",
    "print(\"-\" * 40)\n",
    "if complex_text.strip() == unmasked_text.strip():\n",
    "    print(\"✅ Combined masking and unmasking successful!\")\n",
    "else:\n",
    "    print(\"❌ Unmasking verification failed\")\n",
    "\n",
    "# Performance statistics\n",
    "print(\"\\nPerformance Statistics:\")\n",
    "print(\"-\" * 40)\n",
    "perf_stats = enhanced_masker.get_performance_stats()\n",
    "for key, value in perf_stats.items():\n",
    "    if 'time' in key:\n",
    "        print(f\"{key}: {value:.4f} seconds\")\n",
    "    else:\n",
    "        print(f\"{key}: {value}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Week 5 Enhanced PII testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week5-comparison-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 5: Comparison Test - Regex vs Combined Approach\n",
    "\n",
    "print(\"⚖️  Comparison: Regex vs Combined Approach\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Test text with entities that SpaCy might catch but regex might miss\n",
    "comparison_text = \"\"\"\n",
    "Hi, I'm Dr. Elizabeth Warren from Massachusetts General Hospital.\n",
    "Please contact me at the hospital or reach out to Microsoft Corporation\n",
    "for technical support. The incident happened in Boston, Massachusetts\n",
    "on December 25th, 2023 around 3:45 PM. The affected patient is\n",
    "Mr. Robert Johnson, a 45-year-old software engineer from California.\n",
    "His insurance covers $100,000 in medical expenses.\n",
    "\"\"\"\n",
    "\n",
    "print(\"Test Text for Comparison:\")\n",
    "print(\"-\" * 30)\n",
    "print(comparison_text)\n",
    "\n",
    "# Test 1: Regex-only approach\n",
    "print(\"\\n1. Regex-Only Approach:\")\n",
    "print(\"-\" * 30)\n",
    "regex_masker = EnhancedPIIMasker()\n",
    "regex_masker.set_masking_approach(use_combined=False)\n",
    "masked_regex = regex_masker.mask(comparison_text)\n",
    "print(masked_regex)\n",
    "\n",
    "regex_pii = regex_masker.get_detected_pii()\n",
    "regex_count = sum(len(values) for values in regex_pii.values())\n",
    "print(f\"\\nRegex detected: {regex_count} PII items\")\n",
    "for pii_type, values in regex_pii.items():\n",
    "    print(f\"  {pii_type}: {values}\")\n",
    "\n",
    "# Test 2: Combined approach\n",
    "print(\"\\n2. Combined Approach (Regex + SpaCy):\")\n",
    "print(\"-\" * 40)\n",
    "combined_masker = EnhancedPIIMasker()\n",
    "combined_masker.set_masking_approach(use_combined=True)\n",
    "masked_combined = combined_masker.mask(comparison_text)\n",
    "print(masked_combined)\n",
    "\n",
    "combined_pii = combined_masker.get_detected_pii()\n",
    "combined_count = sum(len(values) for values in combined_pii.values())\n",
    "print(f\"\\nCombined detected: {combined_count} PII items\")\n",
    "for pii_type, values in combined_pii.items():\n",
    "    print(f\"  {pii_type}: {values}\")\n",
    "\n",
    "# Performance comparison\n",
    "print(\"\\n3. Performance Comparison:\")\n",
    "print(\"-\" * 30)\n",
    "regex_stats = regex_masker.get_performance_stats()\n",
    "combined_stats = combined_masker.get_performance_stats()\n",
    "\n",
    "print(f\"Regex processing time: {regex_stats['avg_processing_time']:.4f}s\")\n",
    "print(f\"Combined processing time: {combined_stats['avg_processing_time']:.4f}s\")\n",
    "print(f\"Speed difference: {combined_stats['avg_processing_time'] / regex_stats['avg_processing_time']:.2f}x slower\")\n",
    "\n",
    "print(f\"\\nDetection improvement: {combined_count - regex_count} additional PII items\")\n",
    "print(f\"Detection rate improvement: {((combined_count - regex_count) / max(regex_count, 1)) * 100:.1f}%\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Comparison testing completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week5-homework",
   "metadata": {},
   "source": [
    "## Week 5 Homework Assignment\n",
    "\n",
    "### Tasks\n",
    "1. **Custom Entity Training**:\n",
    "   - Create a custom SpaCy model for domain-specific PII\n",
    "   - Train on cybersecurity-specific entities (vulnerability IDs, threat actor names)\n",
    "   - Evaluate model performance on test data\n",
    "\n",
    "2. **Performance Optimization**:\n",
    "   - Implement batch processing for large documents\n",
    "   - Add caching mechanisms for repeated entities\n",
    "   - Optimize SpaCy pipeline for speed vs accuracy trade-offs\n",
    "\n",
    "3. **Multilingual Support**:\n",
    "   - Extend PII detection to support Spanish and French\n",
    "   - Test with multilingual documents\n",
    "   - Handle code-switching scenarios\n",
    "\n",
    "4. **Advanced Pattern Matching**:\n",
    "   - Implement fuzzy matching for name variations\n",
    "   - Add context-aware PII detection\n",
    "   - Create confidence scoring for detections\n",
    "\n",
    "### Deliverables\n",
    "- Enhanced PII masker with custom entity support\n",
    "- Performance benchmarking report\n",
    "- Multilingual PII detection system\n",
    "- Accuracy evaluation on diverse test datasets\n",
    "\n",
    "### Notes for Students\n",
    "- **Model Size**: Larger SpaCy models are more accurate but slower\n",
    "- **False Positives**: NLP models may over-detect entities\n",
    "- **Context Matters**: Same text may or may not be PII depending on context\n",
    "- **Privacy**: Never use real PII data for training or testing\n",
    "\n",
    "---"
   ]
  }


  },
  {
   "cell_type": "markdown",
   "id": "week6",
   "metadata": {},
   "source": [
    "# Week 6: Interactive System Development\n",
    "\n",
    "## Learning Objectives\n",
    "- Implement the ReACT (Reasoning and Acting) framework\n",
    "- Create an interactive loop for agent-tool interactions\n",
    "- Build a comprehensive cybersecurity analysis system\n",
    "- Integrate all security APIs with intelligent reasoning\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### ReACT Framework\n",
    "The ReACT (Reasoning and Acting) framework enables AI agents to:\n",
    "- **Think**: Reason about the current situation and plan next steps\n",
    "- **Act**: Execute specific actions using available tools\n",
    "- **Observe**: Analyze results and incorporate feedback\n",
    "- **Repeat**: Continue the cycle until the task is complete\n",
    "\n",
    "### ReACT Cycle Components\n",
    "1. **Thought**: Internal reasoning about what to do next\n",
    "2. **Action**: Specific tool or function to execute\n",
    "3. **Observation**: Results from the executed action\n",
    "4. **Final Answer**: Conclusion based on all observations\n",
    "\n",
    "### Benefits for Cybersecurity\n",
    "- **Systematic Analysis**: Structured approach to threat investigation\n",
    "- **Tool Integration**: Seamless use of multiple security APIs\n",
    "- **Transparency**: Clear reasoning trail for audit purposes\n",
    "- **Adaptability**: Can adjust strategy based on findings\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: Setup for Interactive System\n",
    "import re\n",
    "import json\n",
    "import time\n",
    "from typing import Dict, List, Any, Callable, Optional\n",
    "from datetime import datetime\n",
    "import logging\n",
    "\n",
    "# Configure logging for the interactive system\n",
    "logging.basicConfig(\n",
    "    level=logging.INFO,\n",
    "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n",
    ")\n",
    "logger = logging.getLogger(__name__)\n",
    "\n",
    "print(\"✅ Week 6 interactive system setup completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-system-prompt",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: Comprehensive System Prompt for ReACT Framework\n",
    "\n",
    "CYBERSECURITY_REACT_PROMPT = \"\"\"\n",
    "You are CyberShield AI, an advanced cybersecurity analysis agent that uses the ReACT framework to investigate security threats and provide comprehensive analysis.\n",
    "\n",
    "AVAILABLE TOOLS:\n",
    "1. regex_checker(pattern, text) - Check if a regex pattern matches text\n",
    "2. shodan_lookup(ip) - Get detailed information about an IP address from Shodan\n",
    "3. virustotal_lookup(resource, type) - Check reputation of IPs, URLs, or file hashes\n",
    "4. abuseipdb_lookup(ip) - Check IP reputation and abuse reports\n",
    "\n",
    "REACT FRAMEWORK:\n",
    "You must follow this exact format for each step:\n",
    "\n",
    "Thought: [Your reasoning about what to do next]\n",
    "Action: [tool_name]\n",
    "Action Input: [input for the tool]\n",
    "Observation: [Results will be provided here]\n",
    "\n",
    "Continue this cycle until you have enough information, then provide:\n",
    "Final Answer: [Your comprehensive analysis and recommendations]\n",
    "\n",
    "GUIDELINES:\n",
    "- Always start with a Thought about your approach\n",
    "- Use tools systematically to gather comprehensive information\n",
    "- Cross-reference findings from multiple sources\n",
    "- Provide clear risk assessments (LOW, MEDIUM, HIGH, CRITICAL)\n",
    "- Include specific recommendations for mitigation\n",
    "- Explain your reasoning process clearly\n",
    "- If you encounter PII, note that it has been masked for privacy\n",
    "\n",
    "SECURITY ANALYSIS PRIORITIES:\n",
    "1. Identify and assess immediate threats\n",
    "2. Determine scope and impact of incidents\n",
    "3. Provide actionable remediation steps\n",
    "4. Suggest preventive measures\n",
    "5. Ensure compliance with security best practices\n",
    "\n",
    "Remember: You are helping to protect organizations from cyber threats. Be thorough, accurate, and prioritize security.\n",
    "\"\"\"\n",
    "\n",
    "print(\"✅ Cybersecurity ReACT prompt defined!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-tool-manager",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: Tool Manager for ReACT Framework\n",
    "\n",
    "class ToolManager:\n",
    "    \"\"\"\n",
    "    Manages available tools for the ReACT framework and handles tool execution.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        \"\"\"\n",
    "        Initialize the tool manager with available cybersecurity tools.\n",
    "        \"\"\"\n",
    "        self.tools = {\n",
    "            'regex_checker': self._wrap_tool(regex_checker, 'pattern', 'text'),\n",
    "            'shodan_lookup': self._wrap_tool(shodan_lookup, 'ip'),\n",
    "            'virustotal_lookup': self._wrap_tool(virustotal_lookup, 'resource', 'resource_type'),\n",
    "            'abuseipdb_lookup': self._wrap_tool(abuseipdb_lookup, 'ip')\n",
    "        }\n",
    "        \n",
    "        self.tool_usage_stats = {tool_name: 0 for tool_name in self.tools.keys()}\n",
    "        self.execution_history = []\n",
    "    \n",
    "    def _wrap_tool(self, func: Callable, *param_names: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Wrap a tool function with metadata and error handling.\n",
    "        \n",
    "        Args:\n",
    "            func: The tool function to wrap\n",
    "            *param_names: Names of the function parameters\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing the wrapped function and metadata\n",
    "        \"\"\"\n",
    "        return {\n",
    "            'function': func,\n",
    "            'parameters': param_names,\n",
    "            'description': func.__doc__ or f\"Execute {func.__name__}\"\n",
    "        }\n",
    "    \n",
    "    def execute_tool(self, tool_name: str, *args, **kwargs) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Execute a tool with the given arguments.\n",
    "        \n",
    "        Args:\n",
    "            tool_name: Name of the tool to execute\n",
    "            *args: Positional arguments for the tool\n",
    "            **kwargs: Keyword arguments for the tool\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing execution results and metadata\n",
    "        \"\"\"\n",
    "        if tool_name not in self.tools:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}\",\n",
    "                'result': None\n",
    "            }\n",
    "        \n",
    "        try:\n",
    "            start_time = time.time()\n",
    "            \n",
    "            # Execute the tool\n",
    "            tool_func = self.tools[tool_name]['function']\n",
    "            result = tool_func(*args, **kwargs)\n",
    "            \n",
    "            execution_time = time.time() - start_time\n",
    "            \n",
    "            # Update usage statistics\n",
    "            self.tool_usage_stats[tool_name] += 1\n",
    "            \n",
    "            # Record execution history\n",
    "            execution_record = {\n",
    "                'tool': tool_name,\n",
    "                'args': args,\n",
    "                'kwargs': kwargs,\n",
    "                'result': result,\n",
    "                'execution_time': execution_time,\n",
    "                'timestamp': datetime.now().isoformat(),\n",
    "                'success': True\n",
    "            }\n",
    "            self.execution_history.append(execution_record)\n",
    "            \n",
    "            logger.info(f\"Tool '{tool_name}' executed successfully in {execution_time:.3f}s\")\n",
    "            \n",
    "            return {\n",
    "                'success': True,\n",
    "                'result': result,\n",
    "                'execution_time': execution_time,\n",
    "                'tool': tool_name\n",
    "            }\n",
    "            \n",
    "        except Exception as e:\n",
    "            error_msg = f\"Error executing tool '{tool_name}': {str(e)}\"\n",
    "            logger.error(error_msg)\n",
    "            \n",
    "            # Record failed execution\n",
    "            execution_record = {\n",
    "                'tool': tool_name,\n",
    "                'args': args,\n",
    "                'kwargs': kwargs,\n",
    "                'error': error_msg,\n",
    "                'timestamp': datetime.now().isoformat(),\n",
    "                'success': False\n",
    "            }\n",
    "            self.execution_history.append(execution_record)\n",
    "            \n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': error_msg,\n",
    "                'result': None\n",
    "            }\n",
    "    \n",
    "    def get_tool_list(self) -> List[str]:\n",
    "        \"\"\"\n",
    "        Get list of available tools.\n",
    "        \n",
    "        Returns:\n",
    "            List of tool names\n",
    "        \"\"\"\n",
    "        return list(self.tools.keys())\n",
    "    \n",
    "    def get_tool_info(self, tool_name: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get information about a specific tool.\n",
    "        \n",
    "        Args:\n",
    "            tool_name: Name of the tool\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing tool information\n",
    "        \"\"\"\n",
    "        if tool_name not in self.tools:\n",
    "            return {'error': f\"Tool '{tool_name}' not found\"}\n",
    "        \n",
    "        tool_info = self.tools[tool_name].copy()\n",
    "        tool_info['usage_count'] = self.tool_usage_stats[tool_name]\n",
    "        return tool_info\n",
    "    \n",
    "    def get_usage_statistics(self) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get tool usage statistics.\n",
    "        \n",
    "        Returns:\n",
    "            Dict containing usage statistics\n",
    "        \"\"\"\n",
    "        total_executions = sum(self.tool_usage_stats.values())\n",
    "        \n",
    "        return {\n",
    "            'total_executions': total_executions,\n",
    "            'tool_usage': self.tool_usage_stats.copy(),\n",
    "            'execution_history_length': len(self.execution_history),\n",
    "            'most_used_tool': max(self.tool_usage_stats, key=self.tool_usage_stats.get) if total_executions > 0 else None\n",
    "        }\n",
    "\n",
    "print(\"✅ ToolManager class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-react-agent",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: ReACT Agent Implementation\n",
    "\n",
    "class ReACTAgent:\n",
    "    \"\"\"\n",
    "    A cybersecurity agent that uses the ReACT framework to systematically\n",
    "    analyze threats using available tools and reasoning.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, \n",
    "                 model: str = \"gpt-4o\",\n",
    "                 max_iterations: int = 10,\n",
    "                 max_tokens: int = 1500):\n",
    "        \"\"\"\n",
    "        Initialize the ReACT agent.\n",
    "        \n",
    "        Args:\n",
    "            model: OpenAI model to use\n",
    "            max_iterations: Maximum number of ReACT cycles\n",
    "            max_tokens: Maximum tokens per response\n",
    "        \"\"\"\n",
    "        self.agent = EnhancedAgent(\n",
    "            system=CYBERSECURITY_REACT_PROMPT,\n",
    "            model=model,\n",
    "            max_tokens=max_tokens,\n",
    "            temperature=0.1\n",
    "        )\n",
    "        \n",
    "        self.tool_manager = ToolManager()\n",
    "        self.max_iterations = max_iterations\n",
    "        \n",
    "        # ReACT execution tracking\n",
    "        self.current_iteration = 0\n",
    "        self.react_history = []\n",
    "        \n",
    "        # Patterns for parsing ReACT responses\n",
    "        self.thought_pattern = re.compile(r'Thought:\\s*(.+?)(?=\\n(?:Action|Final Answer))', re.DOTALL | re.IGNORECASE)\n",
    "        self.action_pattern = re.compile(r'Action:\\s*(.+?)\\n', re.IGNORECASE)\n",
    "        self.action_input_pattern = re.compile(r'Action Input:\\s*(.+?)(?=\\n(?:Observation|Thought|Final Answer))', re.DOTALL | re.IGNORECASE)\n",
    "        self.final_answer_pattern = re.compile(r'Final Answer:\\s*(.+)', re.DOTALL | re.IGNORECASE)\n",
    "        \n",
    "        logger.info(f\"ReACT Agent initialized with model: {model}\")\n",
    "    \n",
    "    def truncate_response(self, response: Any, max_length: int = 1000) -> str:\n",
    "        \"\"\"\n",
    "        Truncate and format API responses to prevent token limit issues.\n",
    "        \n",
    "        Args:\n",
    "            response: Response to truncate\n",
    "            max_length: Maximum length of response\n",
    "            \n",
    "        Returns:\n",
    "            Truncated response string\n",
    "        \"\"\"\n",
    "        if isinstance(response, dict):\n",
    "            response_str = json.dumps(response, indent=2)\n",
    "        else:\n",
    "            response_str = str(response)\n",
    "        \n",
    "        if len(response_str) > max_length:\n",
    "            return response_str[:max_length] + \"\\n... [Response truncated for brevity]\"\n",
    "        \n",
    "        return response_str\n",
    "    \n",
    "    def parse_action(self, response: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Parse action and action input from agent response.\n",
    "        \n",
    "        Args:\n",
    "            response: Agent response text\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing parsed action information\n",
    "        \"\"\"\n",
    "        # Extract thought\n",
    "        thought_match = self.thought_pattern.search(response)\n",
    "        thought = thought_match.group(1).strip() if thought_match else \"No thought provided\"\n",
    "        \n",
    "        # Extract action\n",
    "        action_match = self.action_pattern.search(response)\n",
    "        if not action_match:\n",
    "            return {\n",
    "                'thought': thought,\n",
    "                'action': None,\n",
    "                'action_input': None,\n",
    "                'error': 'No action found in response'\n",
    "            }\n",
    "        \n",
    "        action = action_match.group(1).strip()\n",
    "        \n",
    "        # Extract action input\n",
    "        action_input_match = self.action_input_pattern.search(response)\n",
    "        action_input = action_input_match.group(1).strip() if action_input_match else \"\"\n",
    "        \n",
    "        return {\n",
    "            'thought': thought,\n",
    "            'action': action,\n",
    "            'action_input': action_input,\n",
    "            'error': None\n",
    "        }\n",
    "    \n",
    "    def execute_action(self, action: str, action_input: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Execute the specified action with given input.\n",
    "        \n",
    "        Args:\n",
    "            action: Tool name to execute\n",
    "            action_input: Input for the tool\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing execution results\n",
    "        \"\"\"\n",
    "        # Parse action input (handle different formats)\n",
    "        try:\n",
    "            # Try to parse as JSON first\n",
    "            if action_input.startswith('{') and action_input.endswith('}'):\n",
    "                input_data = json.loads(action_input)\n",
    "                if isinstance(input_data, dict):\n",
    "                    return self.tool_manager.execute_tool(action, **input_data)\n",
    "            \n",
    "            # Handle comma-separated values\n",
    "            if ',' in action_input:\n",
    "                args = [arg.strip().strip('\"').strip(\"'\") for arg in action_input.split(',')]\n",
    "                return self.tool_manager.execute_tool(action, *args)\n",
    "            \n",
    "            # Handle single argument\n",
    "            clean_input = action_input.strip().strip('\"').strip(\"'\")\n",
    "            return self.tool_manager.execute_tool(action, clean_input)\n",
    "            \n",
    "        except Exception as e:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Failed to parse action input: {str(e)}\",\n",
    "                'result': None\n",
    "            }\n",
    "    \n",
    "    def run(self, query: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Run the ReACT loop to analyze the given query.\n",
    "        \n",
    "        Args:\n",
    "            query: User query to analyze\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing analysis results and execution details\n",
    "        \"\"\"\n",
    "        logger.info(f\"Starting ReACT analysis for query: {query[:100]}...\")\n",
    "        \n",
    "        # Reset for new analysis\n",
    "        self.current_iteration = 0\n",
    "        self.react_history = []\n",
    "        \n",
    "        # Start with the initial query\n",
    "        current_prompt = query\n",
    "        \n",
    "        while self.current_iteration < self.max_iterations:\n",
    "            self.current_iteration += 1\n",
    "            \n",
    "            logger.info(f\"ReACT iteration {self.current_iteration}/{self.max_iterations}\")\n",
    "            \n",
    "            # Get agent response\n",
    "            try:\n",
    "                response = self.agent(current_prompt)\n",
    "            except Exception as e:\n",
    "                error_msg = f\"Agent error in iteration {self.current_iteration}: {str(e)}\"\n",
    "                logger.error(error_msg)\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': error_msg,\n",
    "                    'iterations': self.current_iteration,\n",
    "                    'history': self.react_history\n",
    "                }\n",
    "            \n",
    "            # Check for final answer\n",
    "            final_answer_match = self.final_answer_pattern.search(response)\n",
    "            if final_answer_match:\n",
    "                final_answer = final_answer_match.group(1).strip()\n",
    "                \n",
    "                # Record final iteration\n",
    "                self.react_history.append({\n",
    "                    'iteration': self.current_iteration,\n",
    "                    'type': 'final_answer',\n",
    "                    'response': response,\n",
    "                    'final_answer': final_answer,\n",
    "                    'timestamp': datetime.now().isoformat()\n",
    "                })\n",
    "                \n",
    "                logger.info(f\"ReACT analysis completed in {self.current_iteration} iterations\")\n",
    "                \n",
    "                return {\n",
    "                    'success': True,\n",
    "                    'final_answer': final_answer,\n",
    "                    'iterations': self.current_iteration,\n",
    "                    'history': self.react_history,\n",
    "                    'tool_usage': self.tool_manager.get_usage_statistics()\n",
    "                }\n",
    "            \n",
    "            # Parse action from response\n",
    "            parsed_action = self.parse_action(response)\n",
    "            \n",
    "            if parsed_action['error'] or not parsed_action['action']:\n",
    "                error_msg = parsed_action.get('error', 'No valid action found')\n",
    "                logger.warning(f\"Action parsing failed: {error_msg}\")\n",
    "                \n",
    "                # Try to continue with a prompt to get a valid action\n",
    "                current_prompt = f\"Please provide a valid action from the available tools: {self.tool_manager.get_tool_list()}\"\n",
    "                continue\n",
    "            \n",
    "            # Execute the action\n",
    "            action_result = self.execute_action(\n",
    "                parsed_action['action'], \n",
    "                parsed_action['action_input']\n",
    "            )\n",
    "            \n",
    "            # Unmask PII in action input and result for agent processing\n",
    "            unmasked_input = self.agent.pii_masker.unmask(parsed_action['action_input'])\n",
    "            \n",
    "            if action_result['success']:\n",
    "                # Truncate result to prevent token overflow\n",
    "                truncated_result = self.truncate_response(action_result['result'])\n",
    "                \n",
    "                # Mask PII in the result before sending back to agent\n",
    "                masked_result = self.agent.pii_masker.mask(truncated_result)\n",
    "                \n",
    "                observation = f\"Observation: {masked_result}\"\n",
    "            else:\n",
    "                observation = f\"Observation: Error - {action_result['error']}\"\n",
    "            \n",
    "            # Record this iteration\n",
    "            self.react_history.append({\n",
    "                'iteration': self.current_iteration,\n",
    "                'type': 'action',\n",
    "                'thought': parsed_action['thought'],\n",
    "                'action': parsed_action['action'],\n",
    "                'action_input': unmasked_input,  # Store unmasked for history\n",
    "                'action_result': action_result,\n",
    "                'observation': observation,\n",
    "                'timestamp': datetime.now().isoformat()\n",
    "            })\n",
    "            \n",
    "            # Prepare next prompt with observation\n",
    "            current_prompt = observation\n",
    "        \n",
    "        # If we reach max iterations without final answer\n",
    "        logger.warning(f\"ReACT analysis reached maximum iterations ({self.max_iterations})\")\n",
    "        \n",
    "        return {\n",
    "            'success': False,\n",
    "            'error': f\"Analysis incomplete - reached maximum iterations ({self.max_iterations})\",\n",
    "            'iterations': self.current_iteration,\n",
    "            'history': self.react_history,\n",
    "            'tool_usage': self.tool_manager.get_usage_statistics()\n",
    "        }\n",
    "    \n",
    "    def get_analysis_summary(self) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get a summary of the last analysis performed.\n",
    "        \n",
    "        Returns:\n",
    "            Dict containing analysis summary\n",
    "        \"\"\"\n",
    "        if not self.react_history:\n",
    "            return {'error': 'No analysis history available'}\n",
    "        \n",
    "        # Count actions by type\n",
    "        action_counts = {}\n",
    "        for step in self.react_history:\n",
    "            if step['type'] == 'action':\n",
    "                action = step['action']\n",
    "                action_counts[action] = action_counts.get(action, 0) + 1\n",
    "        \n",
    "        return {\n",
    "            'total_iterations': len(self.react_history),\n",
    "            'actions_performed': action_counts,\n",
    "            'analysis_completed': any(step['type'] == 'final_answer' for step in self.react_history),\n",
    "            'tool_usage': self.tool_manager.get_usage_statistics()\n",
    "        }\n",
    "\n",
    "print(\"✅ ReACTAgent class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: Test the ReACT Agent\n",
    "\n",
    "print(\"🔄 Testing ReACT Agent for Cybersecurity Analysis\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Initialize the ReACT agent\n",
    "react_agent = ReACTAgent(\n",
    "    model=\"gpt-4o\",\n",
    "    max_iterations=8,\n",
    "    max_tokens=1500\n",
    ")\n",
    "\n",
    "print(\"ReACT Agent initialized successfully\")\n",
    "print(f\"Available tools: {react_agent.tool_manager.get_tool_list()}\")\n",
    "\n",
    "# Test query with potential security incident\n",
    "test_query = \"\"\"\n",
    "I need help analyzing a potential security incident. We detected suspicious activity from \n",
    "IP address ************ trying to access our systems. The IP was also seen in connection \n",
    "with the domain malicious-site.com. Can you help me assess the threat level and provide \n",
    "recommendations?\n",
    "\"\"\"\n",
    "\n",
    "print(\"\\nTest Query:\")\n",
    "print(\"-\" * 40)\n",
    "print(test_query)\n",
    "\n",
    "print(\"\\nStarting ReACT Analysis...\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "# Run the analysis\n",
    "analysis_result = react_agent.run(test_query)\n",
    "\n",
    "# Display results\n",
    "if analysis_result['success']:\n",
    "    print(\"\\n✅ Analysis Completed Successfully!\")\n",
    "    print(\"\\nFinal Answer:\")\n",
    "    print(\"-\" * 40)\n",
    "    print(analysis_result['final_answer'])\n",
    "    \n",
    "    print(f\"\\nAnalysis completed in {analysis_result['iterations']} iterations\")\n",
    "    \n",
    "    # Show tool usage\n",
    "    print(\"\\nTool Usage Summary:\")\n",
    "    print(\"-\" * 40)\n",
    "    tool_usage = analysis_result['tool_usage']\n",
    "    print(f\"Total tool executions: {tool_usage['total_executions']}\")\n",
    "    print(f\"Most used tool: {tool_usage['most_used_tool']}\")\n",
    "    for tool, count in tool_usage['tool_usage'].items():\n",
    "        if count > 0:\n",
    "            print(f\"  {tool}: {count} times\")\n",
    "    \n",
    "    # Show iteration details\n",
    "    print(\"\\nIteration Details:\")\n",
    "    print(\"-\" * 40)\n",
    "    for step in analysis_result['history']:\n",
    "        if step['type'] == 'action':\n",
    "            print(f\"Iteration {step['iteration']}:\")\n",
    "            print(f\"  Thought: {step['thought'][:100]}...\")\n",
    "            print(f\"  Action: {step['action']}({step['action_input']})\")\n",
    "            if step['action_result']['success']:\n",
    "                print(f\"  Result: Success\")\n",
    "            else:\n",
    "                print(f\"  Result: Error - {step['action_result']['error']}\")\n",
    "            print()\n",
    "        elif step['type'] == 'final_answer':\n",
    "            print(f\"Final Answer provided in iteration {step['iteration']}\")\n",
    "    \n",
    "else:\n",
    "    print(\"\\n❌ Analysis Failed\")\n",
    "    print(f\"Error: {analysis_result['error']}\")\n",
    "    print(f\"Completed {analysis_result['iterations']} iterations\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Week 6 ReACT testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week6-advanced-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 6: Advanced ReACT Testing - Complex Security Scenario\n",
    "\n",
    "print(\"🔍 Advanced ReACT Testing - Complex Security Scenario\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Complex security incident scenario\n",
    "complex_query = \"\"\"\n",
    "URGENT: Security Incident Report\n",
    "\n",
    "We've detected a multi-stage attack on our infrastructure. Here's what we know:\n",
    "\n",
    "1. Initial compromise from IP *************\n",
    "2. Lateral movement to internal systems\n",
    "3. Data exfiltration attempts to external domain evil-command.net\n",
    "4. Suspicious file hash: d41d8cd98f00b204e9800998ecf8427e\n",
    "5. Multiple failed login attempts from ************\n",
    "\n",
    "Please conduct a comprehensive threat analysis and provide:\n",
    "- Risk assessment for each indicator\n",
    "- Attribution analysis if possible\n",
    "- Immediate containment recommendations\n",
    "- Long-term security improvements\n",
    "\"\"\"\n",
    "\n",
    "print(\"Complex Security Scenario:\")\n",
    "print(\"-\" * 40)\n",
    "print(complex_query)\n",
    "\n",
    "print(\"\\nStarting Comprehensive Analysis...\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "# Create a new agent instance for this test\n",
    "advanced_agent = ReACTAgent(\n",
    "    model=\"gpt-4o\",\n",
    "    max_iterations=12,  # More iterations for complex analysis\n",
    "    max_tokens=2000\n",
    ")\n",
    "\n",
    "# Run the complex analysis\n",
    "complex_result = advanced_agent.run(complex_query)\n",
    "\n",
    "# Display comprehensive results\n",
    "if complex_result['success']:\n",
    "    print(\"\\n✅ Comprehensive Analysis Completed!\")\n",
    "    print(\"\\nThreat Analysis Report:\")\n",
    "    print(\"=\" * 50)\n",
    "    print(complex_result['final_answer'])\n",
    "    \n",
    "    # Analysis summary\n",
    "    summary = advanced_agent.get_analysis_summary()\n",
    "    print(\"\\nAnalysis Summary:\")\n",
    "    print(\"-\" * 30)\n",
    "    print(f\"Total iterations: {summary['total_iterations']}\")\n",
    "    print(f\"Analysis completed: {summary['analysis_completed']}\")\n",
    "    print(\"\\nActions performed:\")\n",
    "    for action, count in summary['actions_performed'].items():\n",
    "        print(f\"  {action}: {count} times\")\n",
    "    \n",
    "    # Detailed execution trace\n",
    "    print(\"\\nDetailed Execution Trace:\")\n",
    "    print(\"-\" * 40)\n",
    "    for i, step in enumerate(complex_result['history'], 1):\n",
    "        if step['type'] == 'action':\n",
    "            print(f\"\\nStep {i}: {step['action']}\")\n",
    "            print(f\"Input: {step['action_input']}\")\n",
    "            if step['action_result']['success']:\n",
    "                result_preview = str(step['action_result']['result'])[:200]\n",
    "                print(f\"Result: {result_preview}...\")\n",
    "            else:\n",
    "                print(f\"Error: {step['action_result']['error']}\")\n",
    "        elif step['type'] == 'final_answer':\n",
    "            print(f\"\\nStep {i}: Final Analysis Provided\")\n",
    "    \n",
    "else:\n",
    "    print(\"\\n❌ Complex Analysis Failed\")\n",
    "    print(f\"Error: {complex_result['error']}\")\n",
    "    \n",
    "    # Show partial results if available\n",
    "    if complex_result['history']:\n",
    "        print(\"\\nPartial Analysis Results:\")\n",
    "        for step in complex_result['history'][-3:]:  # Show last 3 steps\n",
    "            if step['type'] == 'action':\n",
    "                print(f\"- {step['action']}: {step['action_input']}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Advanced ReACT testing completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week6-homework",
   "metadata": {},
   "source": [
    "## Week 6 Homework Assignment\n",
    "\n",
    "### Tasks\n",
    "1. **Enhanced Tool Integration**:\n",
    "   - Add new cybersecurity tools (URLVoid, Hybrid Analysis)\n",
    "   - Implement tool chaining for complex workflows\n",
    "   - Create tool validation and error recovery\n",
    "\n",
    "2. **Advanced ReACT Features**:\n",
    "   - Implement parallel tool execution\n",
    "   - Add confidence scoring for analysis results\n",
    "   - Create analysis templates for common scenarios\n",
    "\n",
    "3. **Performance Optimization**:\n",
    "   - Implement caching for repeated API calls\n",
    "   - Add smart iteration limits based on complexity\n",
    "   - Optimize token usage in long conversations\n",
    "\n",
    "4. **User Interface Development**:\n",
    "   - Create a web interface for ReACT analysis\n",
    "   - Add real-time progress tracking\n",
    "   - Implement analysis result visualization\n",
    "\n",
    "### Deliverables\n",
    "- Enhanced ReACT agent with additional tools\n",
    "- Performance benchmarking results\n",
    "- Web interface prototype\n",
    "- Analysis accuracy evaluation report\n",
    "\n",
    "### Notes for Students\n",
    "- **Token Management**: Long ReACT cycles can be expensive\n",
    "- **Error Handling**: Robust error recovery is crucial for reliability\n",
    "- **Tool Selection**: Choose appropriate tools based on query type\n",
    "- **Validation**: Always validate tool outputs before proceeding\n",
    "\n",
    "---"
   ]
  }


  },
  {
   "cell_type": "markdown",
   "id": "week7",
   "metadata": {},
   "source": [
    "# Week 7: Multimodal Integration\n",
    "\n",
    "## Learning Objectives\n",
    "- Add image analysis capabilities to the cybersecurity agent\n",
    "- Implement GPT-4 Vision for text extraction from images\n",
    "- Create multimodal threat analysis workflows\n",
    "- Handle security-related visual content (screenshots, diagrams, logs)\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### Multimodal AI in Cybersecurity\n",
    "Modern cybersecurity often involves visual information:\n",
    "- **Screenshots**: Error messages, system alerts, dashboard views\n",
    "- **Network Diagrams**: Infrastructure layouts, attack paths\n",
    "- **Log Files**: Visual log analysis, pattern recognition\n",
    "- **Phishing Content**: Email screenshots, fake websites\n",
    "- **Malware Analysis**: Visual code analysis, behavior screenshots\n",
    "\n",
    "### GPT-4 Vision Capabilities\n",
    "GPT-4 Vision can:\n",
    "- **Extract Text**: OCR from images and screenshots\n",
    "- **Understand Context**: Interpret visual elements in context\n",
    "- **Analyze Layouts**: Understand UI elements and structures\n",
    "- **Detect Anomalies**: Identify unusual visual patterns\n",
    "- **Generate Descriptions**: Provide detailed image descriptions\n",
    "\n",
    "### Security Applications\n",
    "- **Incident Analysis**: Analyze screenshots of security events\n",
    "- **Phishing Detection**: Examine suspicious email images\n",
    "- **Log Analysis**: Extract information from log screenshots\n",
    "- **Network Analysis**: Understand network topology diagrams\n",
    "- **Compliance Auditing**: Verify security configurations visually\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Setup for Multimodal Integration\n",
    "!pip install Pillow requests\n",
    "\n",
    "import base64\n",
    "import io\n",
    "from PIL import Image\n",
    "import requests\n",
    "from typing import Union, Dict, Any, List\n",
    "import os\n",
    "\n",
    "print(\"✅ Week 7 multimodal packages installed successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-image-handler",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Image Handler for Multimodal Processing\n",
    "\n",
    "class ImageHandler:\n",
    "    \"\"\"\n",
    "    Handles image processing for multimodal cybersecurity analysis.\n",
    "    Supports loading, processing, and encoding images for AI analysis.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, max_image_size: tuple = (1024, 1024)):\n",
    "        \"\"\"\n",
    "        Initialize the image handler.\n",
    "        \n",
    "        Args:\n",
    "            max_image_size: Maximum dimensions for image processing\n",
    "        \"\"\"\n",
    "        self.max_image_size = max_image_size\n",
    "        self.supported_formats = ['PNG', 'JPEG', 'JPG', 'GIF', 'BMP', 'WEBP']\n",
    "        \n",
    "        logger.info(f\"ImageHandler initialized with max size: {max_image_size}\")\n",
    "    \n",
    "    def load_image_from_file(self, file_path: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Load an image from a local file.\n",
    "        \n",
    "        Args:\n",
    "            file_path: Path to the image file\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing image data and metadata\n",
    "        \"\"\"\n",
    "        try:\n",
    "            if not os.path.exists(file_path):\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': f\"File not found: {file_path}\",\n",
    "                    'image_data': None\n",
    "                }\n",
    "            \n",
    "            # Read and encode the image\n",
    "            with open(file_path, 'rb') as image_file:\n",
    "                image_data = image_file.read()\n",
    "            \n",
    "            # Load with PIL for validation and processing\n",
    "            image = Image.open(io.BytesIO(image_data))\n",
    "            \n",
    "            # Validate format\n",
    "            if image.format not in self.supported_formats:\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': f\"Unsupported image format: {image.format}\",\n",
    "                    'image_data': None\n",
    "                }\n",
    "            \n",
    "            # Resize if necessary\n",
    "            if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:\n",
    "                image.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)\n",
    "                \n",
    "                # Convert back to bytes\n",
    "                output_buffer = io.BytesIO()\n",
    "                image.save(output_buffer, format='PNG')\n",
    "                image_data = output_buffer.getvalue()\n",
    "            \n",
    "            # Encode to base64\n",
    "            base64_image = base64.b64encode(image_data).decode('utf-8')\n",
    "            \n",
    "            return {\n",
    "                'success': True,\n",
    "                'image_data': base64_image,\n",
    "                'format': image.format,\n",
    "                'size': image.size,\n",
    "                'mode': image.mode,\n",
    "                'file_path': file_path\n",
    "            }\n",
    "            \n",
    "        except Exception as e:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Error loading image: {str(e)}\",\n",
    "                'image_data': None\n",
    "            }\n",
    "    \n",
    "    def load_image_from_url(self, url: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Load an image from a URL.\n",
    "        \n",
    "        Args:\n",
    "            url: URL of the image\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing image data and metadata\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Download the image\n",
    "            response = requests.get(url, timeout=30)\n",
    "            response.raise_for_status()\n",
    "            \n",
    "            # Load with PIL\n",
    "            image = Image.open(io.BytesIO(response.content))\n",
    "            \n",
    "            # Validate format\n",
    "            if image.format not in self.supported_formats:\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': f\"Unsupported image format: {image.format}\",\n",
    "                    'image_data': None\n",
    "                }\n",
    "            \n",
    "            # Resize if necessary\n",
    "            if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:\n",
    "                image.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)\n",
    "            \n",
    "            # Convert to base64\n",
    "            output_buffer = io.BytesIO()\n",
    "            image.save(output_buffer, format='PNG')\n",
    "            image_data = output_buffer.getvalue()\n",
    "            base64_image = base64.b64encode(image_data).decode('utf-8')\n",
    "            \n",
    "            return {\n",
    "                'success': True,\n",
    "                'image_data': base64_image,\n",
    "                'format': image.format,\n",
    "                'size': image.size,\n",
    "                'mode': image.mode,\n",
    "                'url': url\n",
    "            }\n",
    "            \n",
    "        except Exception as e:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Error loading image from URL: {str(e)}\",\n",
    "                'image_data': None\n",
    "            }\n",
    "    \n",
    "    def create_sample_security_image(self, image_type: str = \"log\") -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Create a sample security-related image for testing.\n",
    "        \n",
    "        Args:\n",
    "            image_type: Type of security image to create\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing sample image data\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Create a simple image with security-related text\n",
    "            from PIL import ImageDraw, ImageFont\n",
    "            \n",
    "            # Create image\n",
    "            img = Image.new('RGB', (800, 600), color='black')\n",
    "            draw = ImageDraw.Draw(img)\n",
    "            \n",
    "            # Sample security content based on type\n",
    "            if image_type == \"log\":\n",
    "                content = [\n",
    "                    \"SECURITY LOG - CRITICAL ALERT\",\n",
    "                    \"[2024-03-15 14:30:22] FAILED LOGIN ATTEMPT\",\n",
    "                    \"Source IP: ************\",\n",
    "                    \"User: <EMAIL>\",\n",
    "                    \"Attempts: 15 in 5 minutes\",\n",
    "                    \"Status: BLOCKED - Potential brute force attack\",\n",
    "                    \"Action Required: Investigate source IP\"\n",
    "                ]\n",
    "            elif image_type == \"alert\":\n",
    "                content = [\n",
    "                    \"⚠️ SECURITY ALERT ⚠️\",\n",
    "                    \"Malware Detected\",\n",
    "                    \"File: suspicious_document.pdf\",\n",
    "                    \"Hash: d41d8cd98f00b204e9800998ecf8427e\",\n",
    "                    \"Threat Level: HIGH\",\n",
    "                    \"Quarantined: YES\",\n",
    "                    \"Scan Engine: CyberShield AV\"\n",
    "                ]\n",
    "            else:  # network\n",
    "                content = [\n",
    "                    \"NETWORK TRAFFIC ANALYSIS\",\n",
    "                    \"Suspicious Outbound Connection\",\n",
    "                    \"Destination: evil-command.net\",\n",
    "                    \"Port: 443 (HTTPS)\",\n",
    "                    \"Data Transferred: 2.5 MB\",\n",
    "                    \"Classification: Potential Data Exfiltration\",\n",
    "                    \"Recommendation: Block domain\"\n",
    "                ]\n",
    "            \n",
    "            # Draw text\n",
    "            y_position = 50\n",
    "            for line in content:\n",
    "                draw.text((50, y_position), line, fill='white')\n",
    "                y_position += 40\n",
    "            \n",
    "            # Convert to base64\n",
    "            output_buffer = io.BytesIO()\n",
    "            img.save(output_buffer, format='PNG')\n",
    "            image_data = output_buffer.getvalue()\n",
    "            base64_image = base64.b64encode(image_data).decode('utf-8')\n",
    "            \n",
    "            return {\n",
    "                'success': True,\n",
    "                'image_data': base64_image,\n",
    "                'format': 'PNG',\n",
    "                'size': img.size,\n",
    "                'mode': img.mode,\n",
    "                'type': image_type,\n",
    "                'description': f\"Sample {image_type} security image\"\n",
    "            }\n",
    "            \n",
    "        except Exception as e:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Error creating sample image: {str(e)}\",\n",
    "                'image_data': None\n",
    "            }\n",
    "\n",
    "print(\"✅ ImageHandler class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-vision-analyzer",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Vision Analyzer for Security Images\n",
    "\n",
    "class SecurityVisionAnalyzer:\n",
    "    \"\"\"\n",
    "    Analyzes security-related images using GPT-4 Vision.\n",
    "    Specialized for cybersecurity contexts and threat analysis.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, model: str = \"gpt-4o\"):\n",
    "        \"\"\"\n",
    "        Initialize the vision analyzer.\n",
    "        \n",
    "        Args:\n",
    "            model: OpenAI model to use for vision analysis\n",
    "        \"\"\"\n",
    "        self.client = OpenAI(api_key=OPENAI_API_KEY)\n",
    "        self.model = model\n",
    "        self.image_handler = ImageHandler()\n",
    "        \n",
    "        # Analysis templates for different security contexts\n",
    "        self.analysis_templates = {\n",
    "            'general': \"\"\"\n",
    "            Analyze this security-related image and provide:\n",
    "            1. Text extraction (OCR) - extract all visible text\n",
    "            2. Security context - identify what type of security information is shown\n",
    "            3. Key findings - highlight important security indicators\n",
    "            4. Risk assessment - evaluate any threats or issues identified\n",
    "            5. Recommendations - suggest appropriate actions\n",
    "            \"\"\",\n",
    "            \n",
    "            'log_analysis': \"\"\"\n",
    "            This appears to be a security log or alert. Please analyze and provide:\n",
    "            1. Extract all log entries and timestamps\n",
    "            2. Identify security events (failed logins, alerts, errors)\n",
    "            3. Extract IP addresses, usernames, and other indicators\n",
    "            4. Assess threat level based on the events shown\n",
    "            5. Recommend investigation steps\n",
    "            \"\"\",\n",
    "            \n",
    "            'phishing_analysis': \"\"\"\n",
    "            Analyze this image for potential phishing indicators:\n",
    "            1. Extract all text, URLs, and email addresses\n",
    "            2. Identify suspicious elements (urgent language, fake branding)\n",
    "            3. Check for social engineering tactics\n",
    "            4. Assess legitimacy of any organizations mentioned\n",
    "            5. Provide phishing risk score and recommendations\n",
    "            \"\"\",\n",
    "            \n",
    "            'network_diagram': \"\"\"\n",
    "            Analyze this network diagram or infrastructure image:\n",
    "            1. Identify network components and connections\n",
    "            2. Extract IP addresses, hostnames, and network segments\n",
    "            3. Identify potential security vulnerabilities in the topology\n",
    "            4. Assess network segmentation and access controls\n",
    "            5. Recommend security improvements\n",
    "            \"\"\",\n",
    "            \n",
    "            'malware_analysis': \"\"\"\n",
    "            Analyze this malware-related image:\n",
    "            1. Extract file names, hashes, and detection results\n",
    "            2. Identify malware families or threat types\n",
    "            3. Extract IOCs (Indicators of Compromise)\n",
    "            4. Assess threat severity and impact\n",
    "            5. Recommend containment and remediation steps\n",
    "            \"\"\"\n",
    "        }\n",
    "        \n",
    "        logger.info(f\"SecurityVisionAnalyzer initialized with model: {model}\")\n",
    "    \n",
    "    def analyze_image(self, \n",
    "                     image_source: Union[str, Dict[str, Any]], \n",
    "                     analysis_type: str = \"general\",\n",
    "                     custom_prompt: str = None) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Analyze a security-related image using GPT-4 Vision.\n",
    "        \n",
    "        Args:\n",
    "            image_source: File path, URL, or image data dict\n",
    "            analysis_type: Type of analysis to perform\n",
    "            custom_prompt: Custom analysis prompt (overrides template)\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing analysis results\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Load image data\n",
    "            if isinstance(image_source, str):\n",
    "                if image_source.startswith('http'):\n",
    "                    image_data = self.image_handler.load_image_from_url(image_source)\n",
    "                else:\n",
    "                    image_data = self.image_handler.load_image_from_file(image_source)\n",
    "            elif isinstance(image_source, dict) and 'image_data' in image_source:\n",
    "                image_data = image_source\n",
    "            else:\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': 'Invalid image source format',\n",
    "                    'analysis': None\n",
    "                }\n",
    "            \n",
    "            if not image_data['success']:\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': f\"Failed to load image: {image_data['error']}\",\n",
    "                    'analysis': None\n",
    "                }\n",
    "            \n",
    "            # Prepare analysis prompt\n",
    "            if custom_prompt:\n",
    "                analysis_prompt = custom_prompt\n",
    "            elif analysis_type in self.analysis_templates:\n",
    "                analysis_prompt = self.analysis_templates[analysis_type]\n",
    "            else:\n",
    "                analysis_prompt = self.analysis_templates['general']\n",
    "            \n",
    "            # Prepare messages for GPT-4 Vision\n",
    "            messages = [\n",
    "                {\n",
    "                    \"role\": \"user\",\n",
    "                    \"content\": [\n",
    "                        {\n",
    "                            \"type\": \"text\",\n",
    "                            \"text\": f\"You are a cybersecurity expert analyzing security-related images. {analysis_prompt}\"\n",
    "                        },\n",
    "                        {\n",
    "                            \"type\": \"image_url\",\n",
    "                            \"image_url\": {\n",
    "                                \"url\": f\"data:image/png;base64,{image_data['image_data']}\"\n",
    "                            }\n",
    "                        }\n",
    "                    ]\n",
    "                }\n",
    "            ]\n",
    "            \n",
    "            # Make API call to GPT-4 Vision\n",
    "            response = self.client.chat.completions.create(\n",
    "                model=self.model,\n",
    "                messages=messages,\n",
    "                max_tokens=1500\n",
    "            )\n",
    "            \n",
    "            analysis_result = response.choices[0].message.content\n",
    "            \n",
    "            # Extract structured information if possible\n",
    "            structured_analysis = self._extract_structured_info(analysis_result)\n",
    "            \n",
    "            return {\n",
    "                'success': True,\n",
    "                'analysis': analysis_result,\n",
    "                'structured_analysis': structured_analysis,\n",
    "                'analysis_type': analysis_type,\n",
    "                'image_metadata': {\n",
    "                    'format': image_data.get('format'),\n",
    "                    'size': image_data.get('size'),\n",
    "                    'source': image_source if isinstance(image_source, str) else 'data'\n",
    "                },\n",
    "                'tokens_used': response.usage.total_tokens,\n",
    "                'timestamp': datetime.now().isoformat()\n",
    "            }\n",
    "            \n",
    "        except Exception as e:\n",
    "            logger.error(f\"Vision analysis failed: {str(e)}\")\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Vision analysis failed: {str(e)}\",\n",
    "                'analysis': None\n",
    "            }\n",
    "    \n",
    "    def _extract_structured_info(self, analysis_text: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Extract structured information from analysis text.\n",
    "        \n",
    "        Args:\n",
    "            analysis_text: Raw analysis text from GPT-4 Vision\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing structured information\n",
    "        \"\"\"\n",
    "        structured = {\n",
    "            'extracted_text': [],\n",
    "            'ip_addresses': [],\n",
    "            'email_addresses': [],\n",
    "            'urls': [],\n",
    "            'file_hashes': [],\n",
    "            'risk_indicators': [],\n",
    "            'recommendations': []\n",
    "        }\n",
    "        \n",
    "        try:\n",
    "            # Extract IP addresses\n",
    "            ip_pattern = r'\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b'\n",
    "            structured['ip_addresses'] = re.findall(ip_pattern, analysis_text)\n",
    "            \n",
    "            # Extract email addresses\n",
    "            email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'\n",
    "            structured['email_addresses'] = re.findall(email_pattern, analysis_text)\n",
    "            \n",
    "            # Extract URLs\n",
    "            url_pattern = r'https?://(?:[-\\w.])+(?:[:\\d]+)?(?:/(?:[\\w/_.])*(?:\\?(?:[\\w&=%.])*)?(?:#(?:[\\w.])*)?)?'\n",
    "            structured['urls'] = re.findall(url_pattern, analysis_text)\n",
    "            \n",
    "            # Extract potential file hashes (MD5, SHA1, SHA256)\n",
    "            hash_pattern = r'\\b[a-fA-F0-9]{32}\\b|\\b[a-fA-F0-9]{40}\\b|\\b[a-fA-F0-9]{64}\\b'\n",
    "            structured['file_hashes'] = re.findall(hash_pattern, analysis_text)\n",
    "            \n",
    "            # Extract risk indicators (simple keyword matching)\n",
    "            risk_keywords = ['critical', 'high risk', 'malware', 'phishing', 'suspicious', 'threat', 'attack', 'breach']\n",
    "            for keyword in risk_keywords:\n",
    "                if keyword.lower() in analysis_text.lower():\n",
    "                    structured['risk_indicators'].append(keyword)\n",
    "            \n",
    "            # Extract recommendations (lines starting with recommendation keywords)\n",
    "            recommendation_keywords = ['recommend', 'suggest', 'should', 'action required', 'next steps']\n",
    "            lines = analysis_text.split('\\n')\n",
    "            for line in lines:\n",
    "                for keyword in recommendation_keywords:\n",
    "                    if keyword.lower() in line.lower() and len(line.strip()) > 20:\n",
    "                        structured['recommendations'].append(line.strip())\n",
    "                        break\n",
    "            \n",
    "        except Exception as e:\n",
    "            logger.warning(f\"Error extracting structured info: {str(e)}\")\n",
    "        \n",
    "        return structured\n",
    "    \n",
    "    def batch_analyze_images(self, \n",
    "                           image_sources: List[Union[str, Dict[str, Any]]], \n",
    "                           analysis_type: str = \"general\") -> List[Dict[str, Any]]:\n",
    "        \"\"\"\n",
    "        Analyze multiple images in batch.\n",
    "        \n",
    "        Args:\n",
    "            image_sources: List of image sources to analyze\n",
    "            analysis_type: Type of analysis to perform\n",
    "            \n",
    "        Returns:\n",
    "            List of analysis results\n",
    "        \"\"\"\n",
    "        results = []\n",
    "        \n",
    "        for i, image_source in enumerate(image_sources):\n",
    "            logger.info(f\"Analyzing image {i+1}/{len(image_sources)}\")\n",
    "            \n",
    "            result = self.analyze_image(image_source, analysis_type)\n",
    "            result['batch_index'] = i\n",
    "            results.append(result)\n",
    "            \n",
    "            # Add small delay to avoid rate limiting\n",
    "            time.sleep(1)\n",
    "        \n",
    "        return results\n",
    "\n",
    "print(\"✅ SecurityVisionAnalyzer class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-multimodal-agent",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Multimodal Cybersecurity Agent\n",
    "\n",
    "class MultimodalCyberAgent:\n",
    "    \"\"\"\n",
    "    Enhanced cybersecurity agent with multimodal capabilities.\n",
    "    Combines text analysis, image analysis, and ReACT framework.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, \n",
    "                 model: str = \"gpt-4o\",\n",
    "                 max_iterations: int = 10):\n",
    "        \"\"\"\n",
    "        Initialize the multimodal cybersecurity agent.\n",
    "        \n",
    "        Args:\n",
    "            model: OpenAI model to use\n",
    "            max_iterations: Maximum ReACT iterations\n",
    "        \"\"\"\n",
    "        # Initialize core components\n",
    "        self.react_agent = ReACTAgent(model=model, max_iterations=max_iterations)\n",
    "        self.vision_analyzer = SecurityVisionAnalyzer(model=model)\n",
    "        self.image_handler = ImageHandler()\n",
    "        \n",
    "        # Add image analysis tool to the tool manager\n",
    "        self.react_agent.tool_manager.tools['analyze_image'] = {\n",
    "            'function': self._analyze_image_tool,\n",
    "            'parameters': ['image_source', 'analysis_type'],\n",
    "            'description': 'Analyze security-related images using GPT-4 Vision'\n",
    "        }\n",
    "        \n",
    "        # Update system prompt to include image analysis capabilities\n",
    "        enhanced_prompt = CYBERSECURITY_REACT_PROMPT + \"\"\"\n",
    "        \n",
    "ADDITIONAL TOOL:\n",
    "5. analyze_image(image_source, analysis_type) - Analyze security-related images\n",
    "   - image_source: file path, URL, or 'sample_log', 'sample_alert', 'sample_network'\n",
    "   - analysis_type: 'general', 'log_analysis', 'phishing_analysis', 'network_diagram', 'malware_analysis'\n",
    "\n",
    "MULTIMODAL ANALYSIS GUIDELINES:\n",
    "- When users mention images, screenshots, or visual content, use the analyze_image tool\n",
    "- For sample analysis, use 'sample_log', 'sample_alert', or 'sample_network' as image_source\n",
    "- Choose appropriate analysis_type based on the context\n",
    "- Combine image analysis results with other security tools for comprehensive assessment\n",
    "- Extract and analyze any text, IPs, URLs, or other indicators found in images\n",
    "        \"\"\"\n",
    "        \n",
    "        self.react_agent.agent.system_message = enhanced_prompt\n",
    "        \n",
    "        logger.info(\"MultimodalCyberAgent initialized with image analysis capabilities\")\n",
    "    \n",
    "    def _analyze_image_tool(self, image_source: str, analysis_type: str = \"general\") -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Tool wrapper for image analysis.\n",
    "        \n",
    "        Args:\n",
    "            image_source: Image source (file, URL, or sample type)\n",
    "            analysis_type: Type of analysis to perform\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing analysis results\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # Handle sample image requests\n",
    "            if image_source.startswith('sample_'):\n",
    "                sample_type = image_source.replace('sample_', '')\n",
    "                sample_image = self.image_handler.create_sample_security_image(sample_type)\n",
    "                \n",
    "                if not sample_image['success']:\n",
    "                    return {\n",
    "                        'success': False,\n",
    "                        'error': f\"Failed to create sample image: {sample_image['error']}\"\n",
    "                    }\n",
    "                \n",
    "                image_source = sample_image\n",
    "            \n",
    "            # Perform image analysis\n",
    "            result = self.vision_analyzer.analyze_image(image_source, analysis_type)\n",
    "            \n",
    "            if result['success']:\n",
    "                # Format result for tool output\n",
    "                formatted_result = {\n",
    "                    'success': True,\n",
    "                    'analysis': result['analysis'],\n",
    "                    'extracted_indicators': result['structured_analysis'],\n",
    "                    'analysis_type': result['analysis_type'],\n",
    "                    'tokens_used': result['tokens_used']\n",
    "                }\n",
    "                \n",
    "                return formatted_result\n",
    "            else:\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': result['error']\n",
    "                }\n",
    "                \n",
    "        except Exception as e:\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Image analysis tool error: {str(e)}\"\n",
    "            }\n",
    "    \n",
    "    def analyze_security_incident(self, \n",
    "                                query: str, \n",
    "                                images: List[Union[str, Dict[str, Any]]] = None) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Analyze a security incident with optional image evidence.\n",
    "        \n",
    "        Args:\n",
    "            query: Text description of the incident\n",
    "            images: Optional list of image sources for analysis\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing comprehensive analysis results\n",
    "        \"\"\"\n",
    "        try:\n",
    "            # If images are provided, analyze them first\n",
    "            image_analyses = []\n",
    "            if images:\n",
    "                logger.info(f\"Analyzing {len(images)} images before incident analysis\")\n",
    "                \n",
    "                for i, image in enumerate(images):\n",
    "                    image_result = self.vision_analyzer.analyze_image(image, \"general\")\n",
    "                    if image_result['success']:\n",
    "                        image_analyses.append({\n",
    "                            'index': i,\n",
    "                            'analysis': image_result['analysis'],\n",
    "                            'indicators': image_result['structured_analysis']\n",
    "                        })\n",
    "                \n",
    "                # Enhance query with image analysis results\n",
    "                if image_analyses:\n",
    "                    enhanced_query = query + \"\\n\\nAdditional Evidence from Images:\\n\"\n",
    "                    for img_analysis in image_analyses:\n",
    "                        enhanced_query += f\"\\nImage {img_analysis['index'] + 1} Analysis:\\n{img_analysis['analysis']}\\n\"\n",
    "                    query = enhanced_query\n",
    "            \n",
    "            # Run ReACT analysis\n",
    "            react_result = self.react_agent.run(query)\n",
    "            \n",
    "            # Combine results\n",
    "            comprehensive_result = {\n",
    "                'incident_analysis': react_result,\n",
    "                'image_analyses': image_analyses,\n",
    "                'total_images_analyzed': len(image_analyses),\n",
    "                'analysis_timestamp': datetime.now().isoformat()\n",
    "            }\n",
    "            \n",
    "            return comprehensive_result\n",
    "            \n",
    "        except Exception as e:\n",
    "            logger.error(f\"Multimodal incident analysis failed: {str(e)}\")\n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': f\"Multimodal analysis failed: {str(e)}\"\n",
    "            }\n",
    "    \n",
    "    def run_multimodal_analysis(self, query: str) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Run multimodal analysis using the ReACT framework.\n",
    "        \n",
    "        Args:\n",
    "            query: User query that may involve image analysis\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing analysis results\n",
    "        \"\"\"\n",
    "        return self.react_agent.run(query)\n",
    "    \n",
    "    def get_capabilities_summary(self) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get a summary of the agent's capabilities.\n",
    "        \n",
    "        Returns:\n",
    "            Dict containing capability information\n",
    "        \"\"\"\n",
    "        return {\n",
    "            'text_analysis_tools': self.react_agent.tool_manager.get_tool_list(),\n",
    "            'image_analysis_types': list(self.vision_analyzer.analysis_templates.keys()),\n",
    "            'supported_image_formats': self.image_handler.supported_formats,\n",
    "            'max_image_size': self.image_handler.max_image_size,\n",
    "            'model': self.react_agent.agent.model,\n",
    "            'max_iterations': self.react_agent.max_iterations\n",
    "        }\n",
    "\n",
    "print(\"✅ MultimodalCyberAgent class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Test Multimodal Cybersecurity Agent\n",
    "\n",
    "print(\"🖼️ Testing Multimodal Cybersecurity Agent\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Initialize the multimodal agent\n",
    "multimodal_agent = MultimodalCyberAgent(\n",
    "    model=\"gpt-4o\",\n",
    "    max_iterations=8\n",
    ")\n",
    "\n",
    "print(\"Multimodal Agent initialized successfully\")\n",
    "\n",
    "# Show capabilities\n",
    "capabilities = multimodal_agent.get_capabilities_summary()\n",
    "print(\"\\nAgent Capabilities:\")\n",
    "print(\"-\" * 30)\n",
    "print(f\"Text Analysis Tools: {capabilities['text_analysis_tools']}\")\n",
    "print(f\"Image Analysis Types: {capabilities['image_analysis_types']}\")\n",
    "print(f\"Supported Image Formats: {capabilities['supported_image_formats']}\")\n",
    "\n",
    "# Test 1: Simple image analysis request\n",
    "print(\"\\n\\nTest 1: Security Log Analysis\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "log_analysis_query = \"\"\"\n",
    "I need help analyzing a security log that shows suspicious activity. \n",
    "Can you analyze a sample security log image and tell me what threats you identify?\n",
    "Use the sample_log image for this analysis.\n",
    "\"\"\"\n",
    "\n",
    "print(\"Query:\", log_analysis_query.strip())\n",
    "print(\"\\nRunning multimodal analysis...\")\n",
    "\n",
    "result1 = multimodal_agent.run_multimodal_analysis(log_analysis_query)\n",
    "\n",
    "if result1['success']:\n",
    "    print(\"\\n✅ Log Analysis Completed!\")\n",
    "    print(\"\\nFinal Analysis:\")\n",
    "    print(\"-\" * 30)\n",
    "    print(result1['final_answer'])\n",
    "    \n",
    "    print(f\"\\nCompleted in {result1['iterations']} iterations\")\n",
    "    print(f\"Tools used: {result1['tool_usage']['tool_usage']}\")\n",
    "else:\n",
    "    print(f\"\\n❌ Analysis failed: {result1['error']}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Test 1 completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-advanced-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Advanced Multimodal Testing - Complex Incident with Multiple Images\n",
    "\n",
    "print(\"🔍 Advanced Multimodal Testing - Complex Security Incident\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Test 2: Complex incident with multiple image types\n",
    "complex_multimodal_query = \"\"\"\n",
    "CRITICAL SECURITY INCIDENT - Multiple Evidence Sources\n",
    "\n",
    "We're dealing with a sophisticated attack that involves multiple systems. \n",
    "I need you to analyze the following evidence:\n",
    "\n",
    "1. First, analyze a sample security alert image (sample_alert) to understand the initial detection\n",
    "2. Then analyze a sample network traffic log (sample_network) to see the attack progression\n",
    "3. Cross-reference findings with threat intelligence using our security APIs\n",
    "4. Provide a comprehensive incident response plan\n",
    "\n",
    "The incident appears to involve:\n",
    "- Malware detection on endpoint systems\n",
    "- Suspicious network traffic to external domains\n",
    "- Potential data exfiltration attempts\n",
    "\n",
    "Please conduct a thorough multimodal analysis and provide actionable recommendations.\n",
    "\"\"\"\n",
    "\n",
    "print(\"Complex Incident Query:\")\n",
    "print(\"-\" * 40)\n",
    "print(complex_multimodal_query)\n",
    "\n",
    "print(\"\\nStarting comprehensive multimodal analysis...\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Run the complex analysis\n",
    "complex_result = multimodal_agent.run_multimodal_analysis(complex_multimodal_query)\n",
    "\n",
    "if complex_result['success']:\n",
    "    print(\"\\n✅ Comprehensive Multimodal Analysis Completed!\")\n",
    "    print(\"\\nIncident Response Report:\")\n",
    "    print(\"=\" * 40)\n",
    "    print(complex_result['final_answer'])\n",
    "    \n",
    "    # Show detailed execution trace\n",
    "    print(\"\\nExecution Summary:\")\n",
    "    print(\"-\" * 30)\n",
    "    print(f\"Total iterations: {complex_result['iterations']}\")\n",
    "    \n",
    "    # Count different types of tools used\n",
    "    tool_usage = complex_result['tool_usage']['tool_usage']\n",
    "    image_analyses = tool_usage.get('analyze_image', 0)\n",
    "    api_calls = sum(count for tool, count in tool_usage.items() if tool != 'analyze_image')\n",
    "    \n",
    "    print(f\"Image analyses performed: {image_analyses}\")\n",
    "    print(f\"Security API calls made: {api_calls}\")\n",
    "    print(f\"Total tool executions: {complex_result['tool_usage']['total_executions']}\")\n",
    "    \n",
    "    # Show step-by-step breakdown\n",
    "    print(\"\\nStep-by-Step Analysis:\")\n",
    "    print(\"-\" * 30)\n",
    "    for i, step in enumerate(complex_result['history'], 1):\n",
    "        if step['type'] == 'action':\n",
    "            action_type = \"🖼️ Image Analysis\" if step['action'] == 'analyze_image' else \"🔧 Security Tool\"\n",
    "            print(f\"Step {i}: {action_type} - {step['action']}\")\n",
    "            if step['action_result']['success']:\n",
    "                print(f\"  ✅ Success\")\n",
    "            else:\n",
    "                print(f\"  ❌ Error: {step['action_result']['error']}\")\n",
    "        elif step['type'] == 'final_answer':\n",
    "            print(f\"Step {i}: 📋 Final Report Generated\")\n",
    "    \n",
    "else:\n",
    "    print(f\"\\n❌ Complex analysis failed: {complex_result['error']}\")\n",
    "    \n",
    "    # Show what was completed\n",
    "    if complex_result.get('history'):\n",
    "        print(f\"\\nPartial completion: {len(complex_result['history'])} steps completed\")\n",
    "        for step in complex_result['history'][-3:]:\n",
    "            if step['type'] == 'action':\n",
    "                print(f\"- Last action: {step['action']}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Advanced multimodal testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week7-standalone-vision-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 7: Standalone Vision Analysis Testing\n",
    "\n",
    "print(\"👁️ Standalone Vision Analysis Testing\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Test the vision analyzer directly\n",
    "vision_analyzer = SecurityVisionAnalyzer()\n",
    "\n",
    "# Test different types of security image analysis\n",
    "test_scenarios = [\n",
    "    {\n",
    "        'name': 'Security Alert Analysis',\n",
    "        'image_type': 'alert',\n",
    "        'analysis_type': 'malware_analysis'\n",
    "    },\n",
    "    {\n",
    "        'name': 'Network Traffic Analysis', \n",
    "        'image_type': 'network',\n",
    "        'analysis_type': 'network_diagram'\n",
    "    },\n",
    "    {\n",
    "        'name': 'Security Log Analysis',\n",
    "        'image_type': 'log',\n",
    "        'analysis_type': 'log_analysis'\n",
    "    }\n",
    "]\n",
    "\n",
    "for i, scenario in enumerate(test_scenarios, 1):\n",
    "    print(f\"\\nTest {i}: {scenario['name']}\")\n",
    "    print(\"-\" * 40)\n",
    "    \n",
    "    # Create sample image\n",
    "    image_handler = ImageHandler()\n",
    "    sample_image = image_handler.create_sample_security_image(scenario['image_type'])\n",
    "    \n",
    "    if sample_image['success']:\n",
    "        print(f\"✅ Sample {scenario['image_type']} image created\")\n",
    "        print(f\"Image size: {sample_image['size']}\")\n",
    "        \n",
    "        # Analyze the image\n",
    "        analysis_result = vision_analyzer.analyze_image(\n",
    "            sample_image, \n",
    "            scenario['analysis_type']\n",
    "        )\n",
    "        \n",
    "        if analysis_result['success']:\n",
    "            print(f\"\\n📊 Analysis Results:\")\n",
    "            print(analysis_result['analysis'])\n",
    "            \n",
    "            # Show extracted indicators\n",
    "            indicators = analysis_result['structured_analysis']\n",
    "            print(f\"\\n🔍 Extracted Indicators:\")\n",
    "            for indicator_type, values in indicators.items():\n",
    "                if values:\n",
    "                    print(f\"  {indicator_type}: {values}\")\n",
    "            \n",
    "            print(f\"\\n📈 Tokens used: {analysis_result['tokens_used']}\")\n",
    "        else:\n",
    "            print(f\"❌ Analysis failed: {analysis_result['error']}\")\n",
    "    else:\n",
    "        print(f\"❌ Failed to create sample image: {sample_image['error']}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Standalone vision testing completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week7-homework",
   "metadata": {},
   "source": [
    "## Week 7 Homework Assignment\n",
    "\n",
    "### Tasks\n",
    "1. **Advanced Image Processing**:\n",
    "   - Implement image preprocessing (noise reduction, contrast enhancement)\n",
    "   - Add support for PDF document analysis\n",
    "   - Create image comparison capabilities for forensic analysis\n",
    "\n",
    "2. **Specialized Analysis Types**:\n",
    "   - Develop phishing email screenshot analysis\n",
    "   - Create malware behavior screenshot interpretation\n",
    "   - Implement network topology diagram analysis\n",
    "\n",
    "3. **Integration Enhancements**:\n",
    "   - Connect image analysis results with threat intelligence APIs\n",
    "   - Implement automated IOC extraction from images\n",
    "   - Create image-based incident correlation\n",
    "\n",
    "4. **Performance and Accuracy**:\n",
    "   - Benchmark vision analysis accuracy on security datasets\n",
    "   - Optimize image processing for speed and cost\n",
    "   - Implement confidence scoring for analysis results\n",
    "\n",
    "### Deliverables\n",
    "- Enhanced multimodal agent with advanced image processing\n",
    "- Specialized analysis modules for different security contexts\n",
    "- Performance benchmarking report\n",
    "- Accuracy evaluation on real security images\n",
    "\n",
    "### Notes for Students\n",
    "- **Cost Management**: Vision API calls are more expensive than text\n",
    "- **Image Quality**: Higher quality images produce better analysis results\n",
    "- **Context Matters**: Provide clear context for better analysis accuracy\n",
    "- **Privacy**: Never upload real sensitive security images to external APIs\n",
    "\n",
    "---"
   ]
  }


  },
  {
   "cell_type": "markdown",
   "id": "week8",
   "metadata": {},
   "source": [
    "# Week 8: Documentation and Finalization\n",
    "\n",
    "## Learning Objectives\n",
    "- Create comprehensive technical documentation\n",
    "- Implement logging and monitoring systems\n",
    "- Conduct performance testing and optimization\n",
    "- Prepare the system for production deployment\n",
    "- Develop user guides and operational procedures\n",
    "\n",
    "## Theoretical Background\n",
    "\n",
    "### Production Readiness Checklist\n",
    "A production-ready cybersecurity system requires:\n",
    "- **Comprehensive Documentation**: Technical specs, user guides, API documentation\n",
    "- **Robust Logging**: Audit trails, performance metrics, error tracking\n",
    "- **Monitoring & Alerting**: System health, performance thresholds, anomaly detection\n",
    "- **Security Hardening**: Input validation, rate limiting, access controls\n",
    "- **Performance Optimization**: Response times, resource usage, scalability\n",
    "- **Disaster Recovery**: Backup procedures, failover mechanisms, recovery plans\n",
    "\n",
    "### Documentation Standards\n",
    "- **API Documentation**: Clear endpoint descriptions, request/response examples\n",
    "- **User Guides**: Step-by-step instructions with screenshots\n",
    "- **Technical Specifications**: Architecture diagrams, data flows, dependencies\n",
    "- **Operational Procedures**: Deployment, monitoring, troubleshooting guides\n",
    "- **Security Policies**: Access controls, data handling, incident response\n",
    "\n",
    "### Monitoring and Observability\n",
    "- **Application Metrics**: Response times, error rates, throughput\n",
    "- **Business Metrics**: Analysis accuracy, threat detection rates, user satisfaction\n",
    "- **Infrastructure Metrics**: CPU, memory, network, storage utilization\n",
    "- **Security Metrics**: Failed authentications, suspicious activities, compliance status\n",
    "\n",
    "## Implementation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-setup",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Setup for Documentation and Monitoring\n",
    "import json\n",
    "import time\n",
    "import psutil\n",
    "import threading\n",
    "from datetime import datetime, timedelta\n",
    "from typing import Dict, List, Any, Optional\n",
    "from dataclasses import dataclass, asdict\n",
    "from collections import defaultdict, deque\n",
    "import statistics\n",
    "\n",
    "print(\"✅ Week 8 documentation and monitoring setup completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-monitoring",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Comprehensive Monitoring and Logging System\n",
    "\n",
    "@dataclass\n",
    "class PerformanceMetrics:\n",
    "    \"\"\"Data class for storing performance metrics.\"\"\"\n",
    "    timestamp: str\n",
    "    response_time: float\n",
    "    cpu_usage: float\n",
    "    memory_usage: float\n",
    "    api_calls: int\n",
    "    errors: int\n",
    "    tokens_used: int\n",
    "    cost: float\n",
    "\n",
    "@dataclass\n",
    "class SecurityMetrics:\n",
    "    \"\"\"Data class for storing security-related metrics.\"\"\"\n",
    "    timestamp: str\n",
    "    pii_detections: int\n",
    "    threat_analyses: int\n",
    "    high_risk_findings: int\n",
    "    false_positives: int\n",
    "    analysis_accuracy: float\n",
    "\n",
    "class CyberShieldMonitor:\n",
    "    \"\"\"\n",
    "    Comprehensive monitoring system for the CyberShield AI agent.\n",
    "    Tracks performance, security metrics, and system health.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, max_history: int = 1000):\n",
    "        \"\"\"\n",
    "        Initialize the monitoring system.\n",
    "        \n",
    "        Args:\n",
    "            max_history: Maximum number of metrics to keep in memory\n",
    "        \"\"\"\n",
    "        self.max_history = max_history\n",
    "        \n",
    "        # Metrics storage\n",
    "        self.performance_history = deque(maxlen=max_history)\n",
    "        self.security_history = deque(maxlen=max_history)\n",
    "        self.error_log = deque(maxlen=max_history)\n",
    "        self.audit_log = deque(maxlen=max_history)\n",
    "        \n",
    "        # Real-time counters\n",
    "        self.session_stats = {\n",
    "            'start_time': datetime.now(),\n",
    "            'total_requests': 0,\n",
    "            'successful_requests': 0,\n",
    "            'failed_requests': 0,\n",
    "            'total_tokens': 0,\n",
    "            'total_cost': 0.0,\n",
    "            'pii_detections': 0,\n",
    "            'threat_analyses': 0\n",
    "        }\n",
    "        \n",
    "        # Alert thresholds\n",
    "        self.thresholds = {\n",
    "            'max_response_time': 30.0,  # seconds\n",
    "            'max_cpu_usage': 80.0,      # percentage\n",
    "            'max_memory_usage': 80.0,   # percentage\n",
    "            'max_error_rate': 10.0,     # percentage\n",
    "            'max_cost_per_hour': 10.0   # USD\n",
    "        }\n",
    "        \n",
    "        # Monitoring thread\n",
    "        self.monitoring_active = False\n",
    "        self.monitoring_thread = None\n",
    "        \n",
    "        logger.info(\"CyberShieldMonitor initialized\")\n",
    "    \n",
    "    def start_monitoring(self, interval: int = 60):\n",
    "        \"\"\"\n",
    "        Start continuous monitoring.\n",
    "        \n",
    "        Args:\n",
    "            interval: Monitoring interval in seconds\n",
    "        \"\"\"\n",
    "        if self.monitoring_active:\n",
    "            logger.warning(\"Monitoring already active\")\n",
    "            return\n",
    "        \n",
    "        self.monitoring_active = True\n",
    "        self.monitoring_thread = threading.Thread(\n",
    "            target=self._monitoring_loop,\n",
    "            args=(interval,),\n",
    "            daemon=True\n",
    "        )\n",
    "        self.monitoring_thread.start()\n",
    "        logger.info(f\"Monitoring started with {interval}s interval\")\n",
    "    \n",
    "    def stop_monitoring(self):\n",
    "        \"\"\"Stop continuous monitoring.\"\"\"\n",
    "        self.monitoring_active = False\n",
    "        if self.monitoring_thread:\n",
    "            self.monitoring_thread.join(timeout=5)\n",
    "        logger.info(\"Monitoring stopped\")\n",
    "    \n",
    "    def _monitoring_loop(self, interval: int):\n",
    "        \"\"\"Internal monitoring loop.\"\"\"\n",
    "        while self.monitoring_active:\n",
    "            try:\n",
    "                self._collect_system_metrics()\n",
    "                self._check_alerts()\n",
    "                time.sleep(interval)\n",
    "            except Exception as e:\n",
    "                logger.error(f\"Monitoring loop error: {str(e)}\")\n",
    "                time.sleep(interval)\n",
    "    \n",
    "    def _collect_system_metrics(self):\n",
    "        \"\"\"Collect current system metrics.\"\"\"\n",
    "        try:\n",
    "            # Get system metrics\n",
    "            cpu_percent = psutil.cpu_percent(interval=1)\n",
    "            memory = psutil.virtual_memory()\n",
    "            \n",
    "            # Create performance metrics\n",
    "            metrics = PerformanceMetrics(\n",
    "                timestamp=datetime.now().isoformat(),\n",
    "                response_time=0.0,  # Will be updated by request tracking\n",
    "                cpu_usage=cpu_percent,\n",
    "                memory_usage=memory.percent,\n",
    "                api_calls=0,  # Will be updated by request tracking\n",
    "                errors=0,     # Will be updated by error tracking\n",
    "                tokens_used=0,  # Will be updated by request tracking\n",
    "                cost=0.0      # Will be updated by request tracking\n",
    "            )\n",
    "            \n",
    "            self.performance_history.append(metrics)\n",
    "            \n",
    "        except Exception as e:\n",
    "            logger.error(f\"Error collecting system metrics: {str(e)}\")\n",
    "    \n",
    "    def _check_alerts(self):\n",
    "        \"\"\"Check for alert conditions.\"\"\"\n",
    "        if not self.performance_history:\n",
    "            return\n",
    "        \n",
    "        latest = self.performance_history[-1]\n",
    "        alerts = []\n",
    "        \n",
    "        # Check thresholds\n",
    "        if latest.cpu_usage > self.thresholds['max_cpu_usage']:\n",
    "            alerts.append(f\"High CPU usage: {latest.cpu_usage:.1f}%\")\n",
    "        \n",
    "        if latest.memory_usage > self.thresholds['max_memory_usage']:\n",
    "            alerts.append(f\"High memory usage: {latest.memory_usage:.1f}%\")\n",
    "        \n",
    "        if latest.response_time > self.thresholds['max_response_time']:\n",
    "            alerts.append(f\"Slow response time: {latest.response_time:.1f}s\")\n",
    "        \n",
    "        # Log alerts\n",
    "        for alert in alerts:\n",
    "            self.log_alert(alert)\n",
    "    \n",
    "    def log_request(self, \n",
    "                   request_type: str, \n",
    "                   response_time: float, \n",
    "                   success: bool, \n",
    "                   tokens_used: int = 0, \n",
    "                   cost: float = 0.0,\n",
    "                   metadata: Dict[str, Any] = None):\n",
    "        \"\"\"\n",
    "        Log a request with performance metrics.\n",
    "        \n",
    "        Args:\n",
    "            request_type: Type of request (analysis, image_analysis, etc.)\n",
    "            response_time: Time taken to process request\n",
    "            success: Whether request was successful\n",
    "            tokens_used: Number of tokens consumed\n",
    "            cost: Cost of the request\n",
    "            metadata: Additional metadata\n",
    "        \"\"\"\n",
    "        # Update session stats\n",
    "        self.session_stats['total_requests'] += 1\n",
    "        if success:\n",
    "            self.session_stats['successful_requests'] += 1\n",
    "        else:\n",
    "            self.session_stats['failed_requests'] += 1\n",
    "        \n",
    "        self.session_stats['total_tokens'] += tokens_used\n",
    "        self.session_stats['total_cost'] += cost\n",
    "        \n",
    "        # Log to audit trail\n",
    "        audit_entry = {\n",
    "            'timestamp': datetime.now().isoformat(),\n",
    "            'type': 'request',\n",
    "            'request_type': request_type,\n",
    "            'response_time': response_time,\n",
    "            'success': success,\n",
    "            'tokens_used': tokens_used,\n",
    "            'cost': cost,\n",
    "            'metadata': metadata or {}\n",
    "        }\n",
    "        \n",
    "        self.audit_log.append(audit_entry)\n",
    "        \n",
    "        logger.info(f\"Request logged: {request_type} - {'Success' if success else 'Failed'} - {response_time:.3f}s\")\n",
    "    \n",
    "    def log_security_event(self, \n",
    "                          event_type: str, \n",
    "                          severity: str, \n",
    "                          details: Dict[str, Any]):\n",
    "        \"\"\"\n",
    "        Log a security-related event.\n",
    "        \n",
    "        Args:\n",
    "            event_type: Type of security event\n",
    "            severity: Severity level (low, medium, high, critical)\n",
    "            details: Event details\n",
    "        \"\"\"\n",
    "        security_entry = {\n",
    "            'timestamp': datetime.now().isoformat(),\n",
    "            'type': 'security_event',\n",
    "            'event_type': event_type,\n",
    "            'severity': severity,\n",
    "            'details': details\n",
    "        }\n",
    "        \n",
    "        self.audit_log.append(security_entry)\n",
    "        \n",
    "        # Update security stats\n",
    "        if event_type == 'pii_detection':\n",
    "            self.session_stats['pii_detections'] += 1\n",
    "        elif event_type == 'threat_analysis':\n",
    "            self.session_stats['threat_analyses'] += 1\n",
    "        \n",
    "        logger.info(f\"Security event logged: {event_type} - {severity} severity\")\n",
    "    \n",
    "    def log_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):\n",
    "        \"\"\"\n",
    "        Log an error event.\n",
    "        \n",
    "        Args:\n",
    "            error_type: Type of error\n",
    "            error_message: Error message\n",
    "            context: Additional context\n",
    "        \"\"\"\n",
    "        error_entry = {\n",
    "            'timestamp': datetime.now().isoformat(),\n",
    "            'type': 'error',\n",
    "            'error_type': error_type,\n",
    "            'message': error_message,\n",
    "            'context': context or {}\n",
    "        }\n",
    "        \n",
    "        self.error_log.append(error_entry)\n",
    "        logger.error(f\"Error logged: {error_type} - {error_message}\")\n",
    "    \n",
    "    def log_alert(self, alert_message: str):\n",
    "        \"\"\"\n",
    "        Log an alert.\n",
    "        \n",
    "        Args:\n",
    "            alert_message: Alert message\n",
    "        \"\"\"\n",
    "        alert_entry = {\n",
    "            'timestamp': datetime.now().isoformat(),\n",
    "            'type': 'alert',\n",
    "            'message': alert_message\n",
    "        }\n",
    "        \n",
    "        self.audit_log.append(alert_entry)\n",
    "        logger.warning(f\"Alert: {alert_message}\")\n",
    "    \n",
    "    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get performance summary for the specified time period.\n",
    "        \n",
    "        Args:\n",
    "            hours: Number of hours to include in summary\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing performance summary\n",
    "        \"\"\"\n",
    "        cutoff_time = datetime.now() - timedelta(hours=hours)\n",
    "        \n",
    "        # Filter recent metrics\n",
    "        recent_metrics = [\n",
    "            m for m in self.performance_history \n",
    "            if datetime.fromisoformat(m.timestamp) > cutoff_time\n",
    "        ]\n",
    "        \n",
    "        if not recent_metrics:\n",
    "            return {'error': 'No metrics available for the specified period'}\n",
    "        \n",
    "        # Calculate statistics\n",
    "        response_times = [m.response_time for m in recent_metrics if m.response_time > 0]\n",
    "        cpu_usage = [m.cpu_usage for m in recent_metrics]\n",
    "        memory_usage = [m.memory_usage for m in recent_metrics]\n",
    "        \n",
    "        summary = {\n",
    "            'period_hours': hours,\n",
    "            'total_metrics': len(recent_metrics),\n",
    "            'session_stats': self.session_stats.copy(),\n",
    "            'performance': {\n",
    "                'avg_response_time': statistics.mean(response_times) if response_times else 0,\n",
    "                'max_response_time': max(response_times) if response_times else 0,\n",
    "                'avg_cpu_usage': statistics.mean(cpu_usage) if cpu_usage else 0,\n",
    "                'max_cpu_usage': max(cpu_usage) if cpu_usage else 0,\n",
    "                'avg_memory_usage': statistics.mean(memory_usage) if memory_usage else 0,\n",
    "                'max_memory_usage': max(memory_usage) if memory_usage else 0\n",
    "            },\n",
    "            'error_rate': (\n",
    "                self.session_stats['failed_requests'] / \n",
    "                max(self.session_stats['total_requests'], 1) * 100\n",
    "            ),\n",
    "            'uptime_hours': (\n",
    "                datetime.now() - self.session_stats['start_time']\n",
    "            ).total_seconds() / 3600\n",
    "        }\n",
    "        \n",
    "        return summary\n",
    "    \n",
    "    def export_logs(self, filename: str = None) -> str:\n",
    "        \"\"\"\n",
    "        Export all logs to a JSON file.\n",
    "        \n",
    "        Args:\n",
    "            filename: Output filename\n",
    "            \n",
    "        Returns:\n",
    "            Filename of exported logs\n",
    "        \"\"\"\n",
    "        if not filename:\n",
    "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "            filename = f\"cybershield_logs_{timestamp}.json\"\n",
    "        \n",
    "        export_data = {\n",
    "            'export_timestamp': datetime.now().isoformat(),\n",
    "            'session_stats': self.session_stats,\n",
    "            'performance_history': [asdict(m) for m in self.performance_history],\n",
    "            'security_history': [asdict(m) for m in self.security_history],\n",
    "            'error_log': list(self.error_log),\n",
    "            'audit_log': list(self.audit_log),\n",
    "            'thresholds': self.thresholds\n",
    "        }\n",
    "        \n",
    "        with open(filename, 'w', encoding='utf-8') as f:\n",
    "            json.dump(export_data, f, indent=2, ensure_ascii=False)\n",
    "        \n",
    "        logger.info(f\"Logs exported to {filename}\")\n",
    "        return filename\n",
    "\n",
    "print(\"✅ CyberShieldMonitor class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-production-agent",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Production-Ready CyberShield Agent\n",
    "\n",
    "class ProductionCyberShieldAgent:\n",
    "    \"\"\"\n",
    "    Production-ready CyberShield AI agent with comprehensive monitoring,\n",
    "    logging, error handling, and documentation capabilities.\n",
    "    \"\"\"\n",
    "    \n",
    "    def __init__(self, \n",
    "                 model: str = \"gpt-4o\",\n",
    "                 max_iterations: int = 10,\n",
    "                 enable_monitoring: bool = True):\n",
    "        \"\"\"\n",
    "        Initialize the production CyberShield agent.\n",
    "        \n",
    "        Args:\n",
    "            model: OpenAI model to use\n",
    "            max_iterations: Maximum ReACT iterations\n",
    "            enable_monitoring: Whether to enable monitoring\n",
    "        \"\"\"\n",
    "        # Initialize core components\n",
    "        self.multimodal_agent = MultimodalCyberAgent(model=model, max_iterations=max_iterations)\n",
    "        \n",
    "        # Initialize monitoring\n",
    "        self.monitor = CyberShieldMonitor() if enable_monitoring else None\n",
    "        \n",
    "        # Configuration\n",
    "        self.config = {\n",
    "            'model': model,\n",
    "            'max_iterations': max_iterations,\n",
    "            'version': '1.0.0',\n",
    "            'monitoring_enabled': enable_monitoring,\n",
    "            'max_request_size': 1024 * 1024,  # 1MB\n",
    "            'rate_limit_per_minute': 60,\n",
    "            'max_concurrent_requests': 10\n",
    "        }\n",
    "        \n",
    "        # Rate limiting\n",
    "        self.request_timestamps = deque(maxlen=self.config['rate_limit_per_minute'])\n",
    "        self.active_requests = 0\n",
    "        \n",
    "        # Start monitoring if enabled\n",
    "        if self.monitor:\n",
    "            self.monitor.start_monitoring(interval=30)\n",
    "        \n",
    "        logger.info(f\"ProductionCyberShieldAgent initialized - Version {self.config['version']}\")\n",
    "    \n",
    "    def _check_rate_limit(self) -> bool:\n",
    "        \"\"\"\n",
    "        Check if request is within rate limits.\n",
    "        \n",
    "        Returns:\n",
    "            True if request is allowed, False otherwise\n",
    "        \"\"\"\n",
    "        now = time.time()\n",
    "        \n",
    "        # Remove old timestamps\n",
    "        while self.request_timestamps and now - self.request_timestamps[0] > 60:\n",
    "            self.request_timestamps.popleft()\n",
    "        \n",
    "        # Check rate limit\n",
    "        if len(self.request_timestamps) >= self.config['rate_limit_per_minute']:\n",
    "            return False\n",
    "        \n",
    "        # Check concurrent requests\n",
    "        if self.active_requests >= self.config['max_concurrent_requests']:\n",
    "            return False\n",
    "        \n",
    "        return True\n",
    "    \n",
    "    def _validate_input(self, query: str, images: List[str] = None) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Validate input parameters.\n",
    "        \n",
    "        Args:\n",
    "            query: User query\n",
    "            images: Optional list of image sources\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing validation results\n",
    "        \"\"\"\n",
    "        errors = []\n",
    "        \n",
    "        # Validate query\n",
    "        if not query or not isinstance(query, str):\n",
    "            errors.append(\"Query must be a non-empty string\")\n",
    "        elif len(query.encode('utf-8')) > self.config['max_request_size']:\n",
    "            errors.append(f\"Query exceeds maximum size of {self.config['max_request_size']} bytes\")\n",
    "        \n",
    "        # Validate images\n",
    "        if images:\n",
    "            if not isinstance(images, list):\n",
    "                errors.append(\"Images must be provided as a list\")\n",
    "            elif len(images) > 10:  # Reasonable limit\n",
    "                errors.append(\"Maximum 10 images allowed per request\")\n",
    "        \n",
    "        return {\n",
    "            'valid': len(errors) == 0,\n",
    "            'errors': errors\n",
    "        }\n",
    "    \n",
    "    def analyze_security_incident(self, \n",
    "                                query: str, \n",
    "                                images: List[str] = None,\n",
    "                                analysis_type: str = \"comprehensive\") -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Analyze a security incident with comprehensive monitoring and error handling.\n",
    "        \n",
    "        Args:\n",
    "            query: Description of the security incident\n",
    "            images: Optional list of image sources\n",
    "            analysis_type: Type of analysis to perform\n",
    "            \n",
    "        Returns:\n",
    "            Dict containing analysis results and metadata\n",
    "        \"\"\"\n",
    "        start_time = time.time()\n",
    "        request_id = f\"req_{int(start_time)}_{hash(query) % 10000}\"\n",
    "        \n",
    "        # Log request start\n",
    "        logger.info(f\"Starting security incident analysis - Request ID: {request_id}\")\n",
    "        \n",
    "        try:\n",
    "            # Check rate limits\n",
    "            if not self._check_rate_limit():\n",
    "                error_msg = \"Rate limit exceeded. Please try again later.\"\n",
    "                if self.monitor:\n",
    "                    self.monitor.log_error(\"rate_limit\", error_msg, {'request_id': request_id})\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': error_msg,\n",
    "                    'error_type': 'rate_limit',\n",
    "                    'request_id': request_id\n",
    "                }\n",
    "            \n",
    "            # Validate input\n",
    "            validation = self._validate_input(query, images)\n",
    "            if not validation['valid']:\n",
    "                error_msg = f\"Input validation failed: {'; '.join(validation['errors'])}\"\n",
    "                if self.monitor:\n",
    "                    self.monitor.log_error(\"validation\", error_msg, {'request_id': request_id})\n",
    "                return {\n",
    "                    'success': False,\n",
    "                    'error': error_msg,\n",
    "                    'error_type': 'validation',\n",
    "                    'request_id': request_id\n",
    "                }\n",
    "            \n",
    "            # Track active request\n",
    "            self.active_requests += 1\n",
    "            self.request_timestamps.append(start_time)\n",
    "            \n",
    "            # Perform analysis\n",
    "            if images:\n",
    "                # Multimodal analysis\n",
    "                result = self.multimodal_agent.analyze_security_incident(query, images)\n",
    "            else:\n",
    "                # Text-only analysis\n",
    "                result = self.multimodal_agent.run_multimodal_analysis(query)\n",
    "            \n",
    "            # Calculate metrics\n",
    "            response_time = time.time() - start_time\n",
    "            \n",
    "            # Extract metrics from result\n",
    "            tokens_used = 0\n",
    "            cost = 0.0\n",
    "            \n",
    "            if result.get('success'):\n",
    "                if 'incident_analysis' in result:\n",
    "                    # Multimodal result\n",
    "                    incident_result = result['incident_analysis']\n",
    "                    if 'tool_usage' in incident_result:\n",
    "                        tokens_used = incident_result.get('tool_usage', {}).get('total_tokens', 0)\n",
    "                else:\n",
    "                    # Regular ReACT result\n",
    "                    if 'tool_usage' in result:\n",
    "                        tokens_used = result.get('tool_usage', {}).get('total_tokens', 0)\n",
    "                \n",
    "                # Estimate cost (simplified)\n",
    "                cost = tokens_used * 0.00003  # Rough estimate for GPT-4\n",
    "            \n",
    "            # Log metrics\n",
    "            if self.monitor:\n",
    "                self.monitor.log_request(\n",
    "                    request_type='security_analysis',\n",
    "                    response_time=response_time,\n",
    "                    success=result.get('success', False),\n",
    "                    tokens_used=tokens_used,\n",
    "                    cost=cost,\n",
    "                    metadata={\n",
    "                        'request_id': request_id,\n",
    "                        'analysis_type': analysis_type,\n",
    "                        'has_images': bool(images),\n",
    "                        'image_count': len(images) if images else 0\n",
    "                    }\n",
    "                )\n",
    "                \n",
    "                # Log security events\n",
    "                if result.get('success'):\n",
    "                    self.monitor.log_security_event(\n",
    "                        event_type='threat_analysis',\n",
    "                        severity='medium',\n",
    "                        details={\n",
    "                            'request_id': request_id,\n",
    "                            'analysis_type': analysis_type,\n",
    "                            'response_time': response_time\n",
    "                        }\n",
    "                    )\n",
    "            \n",
    "            # Add metadata to result\n",
    "            result['metadata'] = {\n",
    "                'request_id': request_id,\n",
    "                'response_time': response_time,\n",
    "                'tokens_used': tokens_used,\n",
    "                'estimated_cost': cost,\n",
    "                'timestamp': datetime.now().isoformat(),\n",
    "                'version': self.config['version']\n",
    "            }\n",
    "            \n",
    "            logger.info(f\"Security analysis completed - Request ID: {request_id} - Time: {response_time:.3f}s\")\n",
    "            return result\n",
    "            \n",
    "        except Exception as e:\n",
    "            response_time = time.time() - start_time\n",
    "            error_msg = f\"Unexpected error during analysis: {str(e)}\"\n",
    "            \n",
    "            # Log error\n",
    "            if self.monitor:\n",
    "                self.monitor.log_error(\n",
    "                    \"analysis_error\", \n",
    "                    error_msg, \n",
    "                    {\n",
    "                        'request_id': request_id,\n",
    "                        'response_time': response_time,\n",
    "                        'query_length': len(query)\n",
    "                    }\n",
    "                )\n",
    "                \n",
    "                self.monitor.log_request(\n",
    "                    request_type='security_analysis',\n",
    "                    response_time=response_time,\n",
    "                    success=False,\n",
    "                    metadata={'request_id': request_id, 'error': str(e)}\n",
    "                )\n",
    "            \n",
    "            logger.error(f\"Analysis failed - Request ID: {request_id} - Error: {error_msg}\")\n",
    "            \n",
    "            return {\n",
    "                'success': False,\n",
    "                'error': error_msg,\n",
    "                'error_type': 'analysis_error',\n",
    "                'request_id': request_id,\n",
    "                'metadata': {\n",
    "                    'response_time': response_time,\n",
    "                    'timestamp': datetime.now().isoformat()\n",
    "                }\n",
    "            }\n",
    "        \n",
    "        finally:\n",
    "            # Decrement active requests\n",
    "            self.active_requests = max(0, self.active_requests - 1)\n",
    "    \n",
    "    def get_system_status(self) -> Dict[str, Any]:\n",
    "        \"\"\"\n",
    "        Get comprehensive system status.\n",
    "        \n",
    "        Returns:\n",
    "            Dict containing system status information\n",
    "        \"\"\"\n",
    "        status = {\n",
    "            'system': {\n",
    "                'version': self.config['version'],\n",
    "                'model': self.config['model'],\n",
    "                'monitoring_enabled': self.config['monitoring_enabled'],\n",
    "                'active_requests': self.active_requests,\n",
    "                'rate_limit_remaining': max(0, self.config['rate_limit_per_minute'] - len(self.request_timestamps))\n",
    "            },\n",
    "            'capabilities': self.multimodal_agent.get_capabilities_summary()\n",
    "        }\n",
    "        \n",
    "        # Add monitoring data if available\n",
    "        if self.monitor:\n",
    "            status['performance'] = self.monitor.get_performance_summary(hours=1)\n",
    "        \n",
    "        return status\n",
    "    \n",
    "    def generate_documentation(self) -> Dict[str, str]:\n",
    "        \"\"\"\n",
    "        Generate comprehensive system documentation.\n",
    "        \n",
    "        Returns:\n",
    "            Dict containing different types of documentation\n",
    "        \"\"\"\n",
    "        docs = {}\n",
    "        \n",
    "        # API Documentation\n",
    "        docs['api_documentation'] = \"\"\"\n",
    "# CyberShield AI API Documentation\n",
    "\n",
    "## Overview\n",
    "CyberShield AI is a comprehensive cybersecurity analysis platform that combines:\n",
    "- Advanced threat intelligence APIs\n",
    "- PII detection and protection\n",
    "- Multimodal analysis (text and images)\n",
    "- ReACT framework for systematic investigation\n",
    "\n",
    "## Main Endpoint\n",
    "\n",
    "### analyze_security_incident(query, images=None, analysis_type=\"comprehensive\")\n",
    "\n",
    "Analyzes security incidents with optional image evidence.\n",
    "\n",
    "**Parameters:**\n",
    "- `query` (str): Description of the security incident\n",
    "- `images` (List[str], optional): List of image sources (file paths or URLs)\n",
    "- `analysis_type` (str): Type of analysis (\"comprehensive\", \"quick\", \"detailed\")\n",
    "\n",
    "**Returns:**\n",
    "```json\n",
    "{\n",
    "  \"success\": true,\n",
    "  \"final_answer\": \"Comprehensive analysis results...\",\n",
    "  \"iterations\": 5,\n",
    "  \"tool_usage\": {...},\n",
    "  \"metadata\": {\n",
    "    \"request_id\": \"req_123456\",\n",
    "    \"response_time\": 15.3,\n",
    "    \"tokens_used\": 2500,\n",
    "    \"estimated_cost\": 0.075\n",
    "  }\n",
    "}\n",
    "```\n",
    "\n",
    "## Rate Limits\n",
    "- 60 requests per minute\n",
    "- Maximum 10 concurrent requests\n",
    "- Maximum request size: 1MB\n",
    "\n",
    "## Error Handling\n",
    "All errors return a structured response:\n",
    "```json\n",
    "{\n",
    "  \"success\": false,\n",
    "  \"error\": \"Error description\",\n",
    "  \"error_type\": \"validation|rate_limit|analysis_error\",\n",
    "  \"request_id\": \"req_123456\"\n",
    "}\n",
    "```\n",
    "        \"\"\"\n",
    "        \n",
    "        # User Guide\n",
    "        docs['user_guide'] = \"\"\"\n",
    "# CyberShield AI User Guide\n",
    "\n",
    "## Getting Started\n",
    "\n",
    "1. **Initialize the Agent**\n",
    "   ```python\n",
    "   agent = ProductionCyberShieldAgent()\n",
    "   ```\n",
    "\n",
    "2. **Analyze a Security Incident**\n",
    "   ```python\n",
    "   result = agent.analyze_security_incident(\n",
    "       query=\"Suspicious activity from IP ************\",\n",
    "       analysis_type=\"comprehensive\"\n",
    "   )\n",
    "   ```\n",
    "\n",
    "3. **Include Image Evidence**\n",
    "   ```python\n",
    "   result = agent.analyze_security_incident(\n",
    "       query=\"Malware alert on endpoint\",\n",
    "       images=[\"alert_screenshot.png\", \"log_file.jpg\"]\n",
    "   )\n",
    "   ```\n",
    "\n",
    "## Analysis Types\n",
    "\n",
    "- **Comprehensive**: Full ReACT analysis with all available tools\n",
    "- **Quick**: Fast analysis for urgent incidents\n",
    "- **Detailed**: In-depth analysis with extensive tool usage\n",
    "\n",
    "## Best Practices\n",
    "\n",
    "1. **Provide Context**: Include relevant background information\n",
    "2. **Use Images**: Screenshots and logs provide valuable evidence\n",
    "3. **Monitor Performance**: Check system status regularly\n",
    "4. **Review Results**: Always validate AI recommendations\n",
    "\n",
    "## Troubleshooting\n",
    "\n",
    "- **Rate Limit Errors**: Wait before retrying requests\n",
    "- **Validation Errors**: Check input format and size\n",
    "- **Analysis Errors**: Review logs for detailed error information\n",
    "        \"\"\"\n",
    "        \n",
    "        # Technical Specifications\n",
    "        docs['technical_specs'] = f\"\"\"\n",
    "# CyberShield AI Technical Specifications\n",
    "\n",
    "## System Architecture\n",
    "\n",
    "### Core Components\n",
    "- **MultimodalCyberAgent**: Main analysis engine\n",
    "- **ReACTAgent**: Reasoning and acting framework\n",
    "- **SecurityVisionAnalyzer**: Image analysis capabilities\n",
    "- **EnhancedPIIMasker**: PII detection and protection\n",
    "- **CyberShieldMonitor**: Monitoring and logging\n",
    "\n",
    "### Supported APIs\n",
    "- Shodan: Internet device discovery\n",
    "- VirusTotal: Malware and URL analysis\n",
    "- AbuseIPDB: IP reputation checking\n",
    "- OpenAI GPT-4: Language and vision models\n",
    "\n",
    "## Configuration\n",
    "- Model: {self.config['model']}\n",
    "- Version: {self.config['version']}\n",
    "- Max Iterations: {self.config['max_iterations']}\n",
    "- Rate Limit: {self.config['rate_limit_per_minute']}/minute\n",
    "- Max Request Size: {self.config['max_request_size']} bytes\n",
    "\n",
    "## Performance Characteristics\n",
    "- Average Response Time: 10-30 seconds\n",
    "- Token Usage: 1000-5000 tokens per analysis\n",
    "- Cost: $0.03-0.15 per analysis\n",
    "- Accuracy: 85-95% for threat detection\n",
    "\n",
    "## Security Features\n",
    "- PII masking and protection\n",
    "- Input validation and sanitization\n",
    "- Rate limiting and abuse prevention\n",
    "- Comprehensive audit logging\n",
    "- Error handling and recovery\n",
    "        \"\"\"\n",
    "        \n",
    "        return docs\n",
    "    \n",
    "    def shutdown(self):\n",
    "        \"\"\"\n",
    "        Gracefully shutdown the agent.\n",
    "        \"\"\"\n",
    "        logger.info(\"Shutting down CyberShield Agent...\")\n",
    "        \n",
    "        if self.monitor:\n",
    "            # Export final logs\n",
    "            log_file = self.monitor.export_logs()\n",
    "            logger.info(f\"Final logs exported to: {log_file}\")\n",
    "            \n",
    "            # Stop monitoring\n",
    "            self.monitor.stop_monitoring()\n",
    "        \n",
    "        logger.info(\"CyberShield Agent shutdown complete\")\n",
    "\n",
    "print(\"✅ ProductionCyberShieldAgent class implemented successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Test Production CyberShield Agent\n",
    "\n",
    "print(\"🚀 Testing Production CyberShield Agent\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Initialize production agent\n",
    "production_agent = ProductionCyberShieldAgent(\n",
    "    model=\"gpt-4o\",\n",
    "    max_iterations=6,\n",
    "    enable_monitoring=True\n",
    ")\n",
    "\n",
    "print(\"Production agent initialized successfully\")\n",
    "\n",
    "# Test 1: System Status Check\n",
    "print(\"\\nTest 1: System Status Check\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "status = production_agent.get_system_status()\n",
    "print(\"System Status:\")\n",
    "print(f\"  Version: {status['system']['version']}\")\n",
    "print(f\"  Model: {status['system']['model']}\")\n",
    "print(f\"  Monitoring: {status['system']['monitoring_enabled']}\")\n",
    "print(f\"  Active Requests: {status['system']['active_requests']}\")\n",
    "print(f\"  Rate Limit Remaining: {status['system']['rate_limit_remaining']}\")\n",
    "\n",
    "# Test 2: Security Incident Analysis\n",
    "print(\"\\nTest 2: Security Incident Analysis\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "incident_query = \"\"\"\n",
    "SECURITY INCIDENT REPORT\n",
    "\n",
    "We've detected suspicious network activity that requires immediate analysis:\n",
    "\n",
    "1. Multiple failed login attempts from IP ************\n",
    "2. Unusual outbound traffic to domain suspicious-site.com\n",
    "3. Potential malware detection on endpoint systems\n",
    "\n",
    "Please provide a comprehensive threat assessment and recommend immediate actions.\n",
    "\"\"\"\n",
    "\n",
    "print(\"Analyzing security incident...\")\n",
    "print(\"Query:\", incident_query.strip()[:100] + \"...\")\n",
    "\n",
    "# Perform analysis\n",
    "analysis_result = production_agent.analyze_security_incident(\n",
    "    query=incident_query,\n",
    "    analysis_type=\"comprehensive\"\n",
    ")\n",
    "\n",
    "# Display results\n",
    "if analysis_result['success']:\n",
    "    print(\"\\n✅ Analysis Completed Successfully!\")\n",
    "    \n",
    "    # Show metadata\n",
    "    metadata = analysis_result['metadata']\n",
    "    print(\"\\nAnalysis Metadata:\")\n",
    "    print(f\"  Request ID: {metadata['request_id']}\")\n",
    "    print(f\"  Response Time: {metadata['response_time']:.3f}s\")\n",
    "    print(f\"  Tokens Used: {metadata['tokens_used']}\")\n",
    "    print(f\"  Estimated Cost: ${metadata['estimated_cost']:.6f}\")\n",
    "    \n",
    "    # Show analysis summary\n",
    "    if 'final_answer' in analysis_result:\n",
    "        print(\"\\nThreat Assessment Summary:\")\n",
    "        print(\"-\" * 30)\n",
    "        # Show first 500 characters of the analysis\n",
    "        summary = analysis_result['final_answer'][:500]\n",
    "        print(summary + \"...\" if len(analysis_result['final_answer']) > 500 else summary)\n",
    "    \n",
    "    # Show tool usage\n",
    "    if 'tool_usage' in analysis_result:\n",
    "        tool_usage = analysis_result['tool_usage']\n",
    "        print(f\"\\nTool Usage: {tool_usage.get('total_executions', 0)} total executions\")\n",
    "        for tool, count in tool_usage.get('tool_usage', {}).items():\n",
    "            if count > 0:\n",
    "                print(f\"  {tool}: {count} times\")\n",
    "else:\n",
    "    print(f\"\\n❌ Analysis Failed: {analysis_result['error']}\")\n",
    "    print(f\"Error Type: {analysis_result.get('error_type', 'unknown')}\")\n",
    "    print(f\"Request ID: {analysis_result.get('request_id', 'unknown')}\")\n",
    "\n",
    "# Test 3: Performance Monitoring\n",
    "print(\"\\nTest 3: Performance Monitoring\")\n",
    "print(\"=\" * 40)\n",
    "\n",
    "if production_agent.monitor:\n",
    "    perf_summary = production_agent.monitor.get_performance_summary(hours=1)\n",
    "    print(\"Performance Summary (Last Hour):\")\n",
    "    print(f\"  Total Requests: {perf_summary['session_stats']['total_requests']}\")\n",
    "    print(f\"  Successful: {perf_summary['session_stats']['successful_requests']}\")\n",
    "    print(f\"  Failed: {perf_summary['session_stats']['failed_requests']}\")\n",
    "    print(f\"  Error Rate: {perf_summary['error_rate']:.1f}%\")\n",
    "    print(f\"  Total Cost: ${perf_summary['session_stats']['total_cost']:.6f}\")\n",
    "    print(f\"  Uptime: {perf_summary['uptime_hours']:.2f} hours\")\n",
    "else:\n",
    "    print(\"Monitoring not enabled\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"✅ Production agent testing completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-documentation-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Documentation Generation Test\n",
    "\n",
    "print(\"📚 Testing Documentation Generation\\n\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Generate comprehensive documentation\n",
    "docs = production_agent.generate_documentation()\n",
    "\n",
    "print(\"Generated Documentation Types:\")\n",
    "for doc_type in docs.keys():\n",
    "    print(f\"  - {doc_type}\")\n",
    "\n",
    "# Save documentation to files\n",
    "doc_files = []\n",
    "for doc_type, content in docs.items():\n",
    "    filename = f\"cybershield_{doc_type}.md\"\n",
    "    with open(filename, 'w', encoding='utf-8') as f:\n",
    "        f.write(content)\n",
    "    doc_files.append(filename)\n",
    "    print(f\"\\n✅ {doc_type} saved to {filename}\")\n",
    "\n",
    "# Show sample from API documentation\n",
    "print(\"\\nSample from API Documentation:\")\n",
    "print(\"-\" * 40)\n",
    "api_doc_sample = docs['api_documentation'][:500]\n",
    "print(api_doc_sample + \"...\")\n",
    "\n",
    "# Export monitoring logs\n",
    "if production_agent.monitor:\n",
    "    print(\"\\nExporting monitoring logs...\")\n",
    "    log_file = production_agent.monitor.export_logs()\n",
    "    print(f\"✅ Logs exported to: {log_file}\")\n",
    "\n",
    "print(\"\\nGenerated Files:\")\n",
    "all_files = doc_files + ([log_file] if production_agent.monitor else [])\n",
    "for i, filename in enumerate(all_files, 1):\n",
    "    print(f\"  {i}. {filename}\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"✅ Documentation generation completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "week8-final-test",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Week 8: Final Comprehensive Test\n",
    "\n",
    "print(\"🎯 Final Comprehensive System Test\\n\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Test multiple scenarios to validate system robustness\n",
    "test_scenarios = [\n",
    "    {\n",
    "        'name': 'Phishing Email Analysis',\n",
    "        'query': 'Analyze this suspicious email that claims to be from our bank asking for account verification.',\n",
    "        'expected_tools': ['virustotal_lookup', 'abuseipdb_lookup']\n",
    "    },\n",
    "    {\n",
    "        'name': 'Network Intrusion Detection',\n",
    "        'query': 'Detected unauthorized access from IP ************* to our internal systems.',\n",
    "        'expected_tools': ['shodan_lookup', 'abuseipdb_lookup', 'virustotal_lookup']\n",
    "    },\n",
    "    {\n",
    "        'name': 'Malware Sample Analysis',\n",
    "        'query': 'Found suspicious file with hash d41d8cd98f00b204e9800998ecf8427e on employee workstation.',\n",
    "        'expected_tools': ['virustotal_lookup']\n",
    "    }\n",
    "]\n",
    "\n",
    "test_results = []\n",
    "\n",
    "for i, scenario in enumerate(test_scenarios, 1):\n",
    "    print(f\"\\nTest Scenario {i}: {scenario['name']}\")\n",
    "    print(\"-\" * 50)\n",
    "    \n",
    "    start_time = time.time()\n",
    "    \n",
    "    # Run analysis\n",
    "    result = production_agent.analyze_security_incident(\n",
    "        query=scenario['query'],\n",
    "        analysis_type=\"quick\"  # Use quick analysis for testing\n",
    "    )\n",
    "    \n",
    "    test_time = time.time() - start_time\n",
    "    \n",
    "    # Evaluate results\n",
    "    test_result = {\n",
    "        'scenario': scenario['name'],\n",
    "        'success': result.get('success', False),\n",
    "        'response_time': test_time,\n",
    "        'tools_used': [],\n",
    "        'tokens_used': 0,\n",
    "        'cost': 0.0\n",
    "    }\n",
    "    \n",
    "    if result.get('success'):\n",
    "        print(f\"✅ {scenario['name']} - SUCCESS\")\n",
    "        \n",
    "        # Extract tool usage\n",
    "        if 'tool_usage' in result:\n",
    "            tool_usage = result['tool_usage'].get('tool_usage', {})\n",
    "            test_result['tools_used'] = [tool for tool, count in tool_usage.items() if count > 0]\n",
    "        \n",
    "        # Extract metadata\n",
    "        if 'metadata' in result:\n",
    "            metadata = result['metadata']\n",
    "            test_result['tokens_used'] = metadata.get('tokens_used', 0)\n",
    "            test_result['cost'] = metadata.get('estimated_cost', 0.0)\n",
    "        \n",
    "        print(f\"  Response Time: {test_time:.3f}s\")\n",
    "        print(f\"  Tools Used: {test_result['tools_used']}\")\n",
    "        print(f\"  Tokens: {test_result['tokens_used']}\")\n",
    "        print(f\"  Cost: ${test_result['cost']:.6f}\")\n",
    "        \n",
    "        # Check if expected tools were used\n",
    "        expected_used = any(tool in test_result['tools_used'] for tool in scenario['expected_tools'])\n",
    "        if expected_used:\n",
    "            print(f\"  ✅ Expected tools were used\")\n",
    "        else:\n",
    "            print(f\"  ⚠️  Expected tools not used: {scenario['expected_tools']}\")\n",
    "    else:\n",
    "        print(f\"❌ {scenario['name']} - FAILED\")\n",
    "        print(f\"  Error: {result.get('error', 'Unknown error')}\")\n",
    "        print(f\"  Error Type: {result.get('error_type', 'unknown')}\")\n",
    "    \n",
    "    test_results.append(test_result)\n",
    "    \n",
    "    # Small delay between tests\n",
    "    time.sleep(2)\n",
    "\n",
    "# Final Summary\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"📊 FINAL TEST SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "successful_tests = sum(1 for result in test_results if result['success'])\n",
    "total_tests = len(test_results)\n",
    "success_rate = (successful_tests / total_tests) * 100\n",
    "\n",
    "print(f\"\\nOverall Results:\")\n",
    "print(f\"  Tests Passed: {successful_tests}/{total_tests} ({success_rate:.1f}%)\")\n",
    "print(f\"  Average Response Time: {statistics.mean([r['response_time'] for r in test_results]):.3f}s\")\n",
    "print(f\"  Total Tokens Used: {sum(r['tokens_used'] for r in test_results)}\")\n",
    "print(f\"  Total Cost: ${sum(r['cost'] for r in test_results):.6f}\")\n",
    "\n",
    "# System health check\n",
    "final_status = production_agent.get_system_status()\n",
    "print(f\"\\nSystem Health:\")\n",
    "print(f\"  Active Requests: {final_status['system']['active_requests']}\")\n",
    "print(f\"  Rate Limit Status: {final_status['system']['rate_limit_remaining']} remaining\")\n",
    "\n",
    "if production_agent.monitor:\n",
    "    final_perf = production_agent.monitor.get_performance_summary(hours=1)\n",
    "    print(f\"  Total Session Requests: {final_perf['session_stats']['total_requests']}\")\n",
    "    print(f\"  Session Error Rate: {final_perf['error_rate']:.1f}%\")\n",
    "    print(f\"  Session Cost: ${final_perf['session_stats']['total_cost']:.6f}\")\n",
    "\n",
    "# Shutdown\n",
    "print(\"\\nShutting down production agent...\")\n",
    "production_agent.shutdown()\n",
    "\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"🎉 CYBERSHIELD AI COURSE COMPLETED SUCCESSFULLY!\")\n",
    "print(\"=\" * 60)\n",
    "print(\"\\nCongratulations! You have successfully completed all 8 weeks of the\")\n",
    "print(\"CyberShield AI course and built a production-ready cybersecurity agent.\")\n",
    "print(\"\\nKey Achievements:\")\n",
    "print(\"  ✅ PII Detection and Masking\")\n",
    "print(\"  ✅ Security API Integration\")\n",
    "print(\"  ✅ ReACT Framework Implementation\")\n",
    "print(\"  ✅ Multimodal Analysis Capabilities\")\n",
    "print(\"  ✅ Production Monitoring and Logging\")\n",
    "print(\"  ✅ Comprehensive Documentation\")\n",
    "print(\"\\nYour CyberShield AI agent is now ready for production deployment!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "week8-homework",
   "metadata": {},
   "source": [
    "## Week 8 Final Project Assignment\n",
    "\n",
    "### Capstone Project: Production Deployment\n",
    "\n",
    "**Objective**: Deploy your CyberShield AI agent in a production environment with comprehensive monitoring, documentation, and operational procedures.\n",
    "\n",
    "### Tasks\n",
    "\n",
    "1. **Production Deployment**:\n",
    "   - Deploy the agent using Docker containers\n",
    "   - Set up load balancing and auto-scaling\n",
    "   - Implement health checks and monitoring\n",
    "   - Configure logging and alerting\n",
    "\n",
    "2. **Security Hardening**:\n",
    "   - Implement authentication and authorization\n",
    "   - Set up API rate limiting and abuse prevention\n",
    "   - Configure secure communication (HTTPS/TLS)\n",
    "   - Implement input validation and sanitization\n",
    "\n",
    "3. **Operational Excellence**:\n",
    "   - Create runbooks for common operations\n",
    "   - Implement backup and disaster recovery\n",
    "   - Set up performance monitoring dashboards\n",
    "   - Create incident response procedures\n",
    "\n",
    "4. **Quality Assurance**:\n",
    "   - Develop comprehensive test suites\n",
    "   - Implement continuous integration/deployment\n",
    "   - Conduct security penetration testing\n",
    "   - Perform load and stress testing\n",
    "\n",
    "### Deliverables\n",
    "\n",
    "1. **Deployed System**:\n",
    "   - Live production deployment\n",
    "   - Monitoring dashboards\n",
    "   - Documentation portal\n",
    "\n",
    "2. **Documentation Package**:\n",
    "   - Architecture documentation\n",
    "   - API documentation\n",
    "   - User guides\n",
    "   - Operational runbooks\n",
    "   - Security policies\n",
    "\n",
    "3. **Testing Reports**:\n",
    "   - Performance benchmarks\n",
    "   - Security assessment\n",
    "   - Accuracy evaluation\n",
    "   - Load testing results\n",
    "\n",
    "4. **Presentation**:\n",
    "   - System demonstration\n",
    "   - Architecture overview\n",
    "   - Performance metrics\n",
    "   - Future roadmap\n",
    "\n",
    "### Evaluation Criteria\n",
    "\n",
    "- **Functionality** (25%): All features working correctly\n",
    "- **Performance** (20%): Response times and scalability\n",
    "- **Security** (20%): Security measures and compliance\n",
    "- **Documentation** (15%): Quality and completeness\n",
    "- **Operations** (10%): Monitoring and maintenance\n",
    "- **Innovation** (10%): Creative solutions and improvements\n",
    "\n",
    "### Success Metrics\n",
    "\n",
    "- System uptime > 99.5%\n",
    "- Average response time < 15 seconds\n",
    "- Threat detection accuracy > 90%\n",
    "- Zero security vulnerabilities\n",
    "- Complete documentation coverage\n",
    "\n",
    "---\n",
    "\n",
    "## Course Conclusion\n",
    "\n",
    "Congratulations on completing the CyberShield AI course! You have successfully:\n",
    "\n",
    "1. **Mastered Core Technologies**: PII protection, API integration, AI agents\n",
    "2. **Built Production Systems**: Monitoring, logging, error handling\n",
    "3. **Implemented Best Practices**: Security, performance, documentation\n",
    "4. **Created Real Value**: A working cybersecurity analysis platform\n",
    "\n",
    "### Next Steps\n",
    "\n",
    "- **Continuous Learning**: Stay updated with cybersecurity trends\n",
    "- **Community Engagement**: Share your work and learn from others\n",
    "- **Professional Development**: Consider cybersecurity certifications\n",
    "- **Innovation**: Explore new AI applications in security\n",
    "\n",
    "### Resources for Continued Learning\n",
    "\n",
    "- **SANS Institute**: Advanced cybersecurity training\n",
    "- **NIST Cybersecurity Framework**: Industry standards\n",
    "- **OpenAI Documentation**: Latest AI capabilities\n",
    "- **Security Communities**: OWASP, DEF CON, BSides\n",
    "\n",
    "**Thank you for your dedication and hard work throughout this course!**\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "course-conclusion",
   "metadata": {},
   "source": [
    "# 🎉 Course Completion Certificate\n",
    "\n",
    "---\n",
    "\n",
    "## Certificate of Completion\n",
    "\n",
    "**CyberShield AI: Advanced Cybersecurity Agent Development**\n",
    "\n",
    "This certifies that the student has successfully completed an intensive 8-week course in developing production-ready AI-powered cybersecurity systems.\n",
    "\n",
    "### Skills Mastered:\n",
    "- ✅ Advanced PII Detection and Protection\n",
    "- ✅ Security API Integration (Shodan, VirusTotal, AbuseIPDB)\n",
    "- ✅ ReACT Framework Implementation\n",
    "- ✅ Multimodal AI Analysis (Text + Images)\n",
    "- ✅ Production System Development\n",
    "- ✅ Monitoring and Observability\n",
    "- ✅ Security Best Practices\n",
    "- ✅ Technical Documentation\n",
    "\n",
    "### Final Project:\n",
    "Production-ready CyberShield AI agent capable of:\n",
    "- Comprehensive threat analysis\n",
    "- Real-time security monitoring\n",
    "- Multimodal evidence processing\n",
    "- Automated incident response\n",
    "\n",
    "**Course Instructor**: Kashyap K. R. Kambhatla, Ph.D.  \n",
    "**Course Duration**: 8 Weeks  \n",
    "**Completion Date**: March 2024  \n",
    "**Course Version**: 1.0.0  \n",
    "\n",
    "---\n",
    "\n",
    "*This certificate represents significant achievement in the intersection of artificial intelligence and cybersecurity, preparing graduates for advanced roles in security engineering, threat analysis, and AI system development.*\n",
    "\n",
    "---"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}